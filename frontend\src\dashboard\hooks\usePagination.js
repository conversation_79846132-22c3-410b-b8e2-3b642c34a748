// src/dashboard/hooks/usePagination.js
import { useState, useCallback, useMemo } from 'react';

const usePagination = (initialPage = 1, initialLimit = 20) => {
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [limit, setLimit] = useState(initialLimit);
  const [totalItems, setTotalItems] = useState(0);

  const totalPages = useMemo(() => {
    return Math.ceil(totalItems / limit);
  }, [totalItems, limit]);

  const hasNextPage = useMemo(() => {
    return currentPage < totalPages;
  }, [currentPage, totalPages]);

  const hasPrevPage = useMemo(() => {
    return currentPage > 1;
  }, [currentPage]);

  const startIndex = useMemo(() => {
    return (currentPage - 1) * limit + 1;
  }, [currentPage, limit]);

  const endIndex = useMemo(() => {
    return Math.min(currentPage * limit, totalItems);
  }, [currentPage, limit, totalItems]);

  const goToPage = useCallback((page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  }, [totalPages]);

  const goToNextPage = useCallback(() => {
    if (hasNextPage) {
      setCurrentPage(prev => prev + 1);
    }
  }, [hasNextPage]);

  const goToPrevPage = useCallback(() => {
    if (hasPrevPage) {
      setCurrentPage(prev => prev - 1);
    }
  }, [hasPrevPage]);

  const goToFirstPage = useCallback(() => {
    setCurrentPage(1);
  }, []);

  const goToLastPage = useCallback(() => {
    setCurrentPage(totalPages);
  }, [totalPages]);

  const changeLimit = useCallback((newLimit) => {
    setLimit(newLimit);
    setCurrentPage(1); // Reset to first page when changing limit
  }, []);

  const updateTotalItems = useCallback((total) => {
    setTotalItems(total);
    // If current page is beyond the new total pages, go to last page
    const newTotalPages = Math.ceil(total / limit);
    if (currentPage > newTotalPages && newTotalPages > 0) {
      setCurrentPage(newTotalPages);
    }
  }, [currentPage, limit]);

  const reset = useCallback(() => {
    setCurrentPage(initialPage);
    setLimit(initialLimit);
    setTotalItems(0);
  }, [initialPage, initialLimit]);

  const getPageNumbers = useCallback((maxVisible = 5) => {
    const pages = [];
    const half = Math.floor(maxVisible / 2);
    
    let start = Math.max(1, currentPage - half);
    let end = Math.min(totalPages, start + maxVisible - 1);
    
    // Adjust start if we're near the end
    if (end - start + 1 < maxVisible) {
      start = Math.max(1, end - maxVisible + 1);
    }
    
    for (let i = start; i <= end; i++) {
      pages.push(i);
    }
    
    return pages;
  }, [currentPage, totalPages]);

  const paginationInfo = useMemo(() => ({
    currentPage,
    totalPages,
    totalItems,
    limit,
    hasNextPage,
    hasPrevPage,
    startIndex,
    endIndex
  }), [
    currentPage,
    totalPages,
    totalItems,
    limit,
    hasNextPage,
    hasPrevPage,
    startIndex,
    endIndex
  ]);

  return {
    // State
    currentPage,
    totalPages,
    totalItems,
    limit,
    hasNextPage,
    hasPrevPage,
    startIndex,
    endIndex,
    
    // Actions
    goToPage,
    goToNextPage,
    goToPrevPage,
    goToFirstPage,
    goToLastPage,
    changeLimit,
    updateTotalItems,
    reset,
    
    // Utilities
    getPageNumbers,
    paginationInfo
  };
};

export default usePagination;
