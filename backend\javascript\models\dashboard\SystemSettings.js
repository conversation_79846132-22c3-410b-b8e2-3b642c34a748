// models/dashboard/SystemSettings.js
import mongoose from 'mongoose';

/**
 * Defines the schema for global system settings and default limits
 */
const SystemSettingsSchema = new mongoose.Schema({
  // Default limits for new users
  defaultLimits: {
    starter: {
      uploadLimit: { type: Number, default: 5 },
      messageLimit: { type: Number, default: 50 },
      businessPlanLimit: { type: Number, default: 2 },
      investorPitchLimit: { type: Number, default: 1 },
      businessQALimit: { type: Number, default: 10 },
    },
    pro: {
      uploadLimit: { type: Number, default: 100 },
      messageLimit: { type: Number, default: 1000 },
      businessPlanLimit: { type: Number, default: 20 },
      investorPitchLimit: { type: Number, default: 10 },
      businessQALimit: { type: Number, default: 100 },
    },
    enterprise: {
      uploadLimit: { type: Number, default: -1 }, // -1 means unlimited
      messageLimit: { type: Number, default: -1 },
      businessPlanLimit: { type: Number, default: -1 },
      investorPitchLimit: { type: Number, default: -1 },
      businessQALimit: { type: Number, default: -1 },
    },
  },

  // System-wide settings
  systemConfig: {
    maintenanceMode: { type: Boolean, default: false },
    registrationEnabled: { type: Boolean, default: true },
    maxFileSize: { type: Number, default: 10 }, // MB
    allowedFileTypes: {
      type: [String],
      default: ['pdf', 'doc', 'docx', 'txt'],
    },
    sessionTimeout: { type: Number, default: 24 }, // hours
    passwordMinLength: { type: Number, default: 6 },
    requireEmailVerification: { type: Boolean, default: true },
  },

  // Rate limiting settings
  rateLimiting: {
    apiCallsPerMinute: { type: Number, default: 100 },
    uploadsPerHour: { type: Number, default: 10 },
    messagesPerMinute: { type: Number, default: 20 },
  },

  // Email settings
  emailConfig: {
    fromName: { type: String, default: 'Dosky Platform' },
    fromEmail: { type: String, default: '<EMAIL>' },
    supportEmail: { type: String, default: '<EMAIL>' },
    welcomeEmailEnabled: { type: Boolean, default: true },
    notificationEmailEnabled: { type: Boolean, default: true },
  },

  // Analytics and monitoring
  analytics: {
    trackUserActivity: { type: Boolean, default: true },
    retentionPeriod: { type: Number, default: 90 }, // days
    enableDetailedLogging: { type: Boolean, default: false },
  },

  // Security settings
  security: {
    maxLoginAttempts: { type: Number, default: 5 },
    lockoutDuration: { type: Number, default: 30 }, // minutes
    requireTwoFactor: { type: Boolean, default: false },
    passwordExpiryDays: { type: Number, default: 90 },
    sessionSecurityLevel: {
      type: String,
      enum: ['low', 'medium', 'high'],
      default: 'medium',
    },
  },

  // Feature flags
  features: {
    aiChatEnabled: { type: Boolean, default: true },
    businessToolsEnabled: { type: Boolean, default: true },
    pdfAnalysisEnabled: { type: Boolean, default: true },
    paymentProcessingEnabled: { type: Boolean, default: true },
    socialLoginEnabled: { type: Boolean, default: false },
  },

  // Last updated information
  lastUpdatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: false,
  },
  
  version: {
    type: String,
    default: '1.0.0',
  },
}, { timestamps: true });

// Ensure only one settings document exists
SystemSettingsSchema.index({}, { unique: true });

// Static method to get current settings (creates default if none exist)
SystemSettingsSchema.statics.getCurrentSettings = async function() {
  let settings = await this.findOne();
  if (!settings) {
    settings = await this.create({});
  }
  return settings;
};

// Method to update specific setting category
SystemSettingsSchema.methods.updateCategory = function(category, updates) {
  if (this[category]) {
    Object.assign(this[category], updates);
    return this.save();
  }
  throw new Error(`Invalid settings category: ${category}`);
};

// Method to reset to defaults
SystemSettingsSchema.methods.resetToDefaults = function() {
  const defaultSettings = new this.constructor();
  Object.keys(defaultSettings.toObject()).forEach(key => {
    if (key !== '_id' && key !== '__v' && key !== 'createdAt' && key !== 'updatedAt') {
      this[key] = defaultSettings[key];
    }
  });
  return this.save();
};

// Method to validate limits
SystemSettingsSchema.methods.validateLimits = function() {
  const plans = ['starter', 'pro', 'enterprise'];
  const limitTypes = ['uploadLimit', 'messageLimit', 'businessPlanLimit', 'investorPitchLimit', 'businessQALimit'];
  
  for (const plan of plans) {
    for (const limitType of limitTypes) {
      const value = this.defaultLimits[plan][limitType];
      if (value < -1 || (value === 0 && plan !== 'starter')) {
        throw new Error(`Invalid ${limitType} for ${plan} plan: ${value}`);
      }
    }
  }
  return true;
};

// Pre-save validation
SystemSettingsSchema.pre('save', function(next) {
  try {
    this.validateLimits();
    next();
  } catch (error) {
    next(error);
  }
});

const SystemSettings = mongoose.model('SystemSettings', SystemSettingsSchema);

export default SystemSettings;
