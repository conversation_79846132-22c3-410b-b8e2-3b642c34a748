// src/dashboard/pages/UserManagement.jsx
import React, { useState, useEffect } from 'react';
import { 
  FiPlus, 
  FiDownload, 
  FiUpload,
  FiAlertTriangle,
  FiUser,
  FiMail,
  FiCalendar,
  FiSettings
} from 'react-icons/fi';
import { DataTable, Modal, LoadingSpinner, useToast } from '../components';

const UserManagement = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState(null);
  const [selectedUser, setSelectedUser] = useState(null);
  const [showUserModal, setShowUserModal] = useState(false);
  const [showLimitsModal, setShowLimitsModal] = useState(false);
  const [filters, setFilters] = useState({
    search: '',
    planFilter: '',
    statusFilter: '',
    page: 1
  });

  const { success, error: showError } = useToast();

  useEffect(() => {
    fetchUsers();
  }, [filters]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams({
        page: filters.page,
        search: filters.search,
        planFilter: filters.planFilter,
        statusFilter: filters.statusFilter
      });

      const response = await fetch(`http://localhost:3001/api/dashboard/users?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }

      const result = await response.json();
      setUsers(result.data.users);
      setPagination(result.data.pagination);
    } catch (err) {
      console.error('Error fetching users:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (searchTerm) => {
    setFilters(prev => ({ ...prev, search: searchTerm, page: 1 }));
  };

  const handlePageChange = (page) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const handleRowAction = async (action, user) => {
    switch (action) {
      case 'view':
        setSelectedUser(user);
        setShowUserModal(true);
        break;
      case 'edit':
        setSelectedUser(user);
        setShowUserModal(true);
        break;
      case 'limits':
        setSelectedUser(user);
        setShowLimitsModal(true);
        break;
      case 'delete':
        if (window.confirm('Are you sure you want to delete this user?')) {
          await deleteUser(user.id);
        }
        break;
      default:
        break;
    }
  };

  const deleteUser = async (userId) => {
    try {
      const response = await fetch(`http://localhost:3001/api/dashboard/users/${userId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to delete user');
      }

      fetchUsers(); // Refresh the list
      success('User deleted successfully');
    } catch (err) {
      console.error('Error deleting user:', err);
      showError('Failed to delete user: ' + err.message);
    }
  };

  const updateUserLimits = async (userId, limits) => {
    try {
      const response = await fetch(`http://localhost:3001/api/dashboard/users/${userId}/limits`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        },
        body: JSON.stringify({ limits })
      });

      if (!response.ok) {
        throw new Error('Failed to update user limits');
      }

      setShowLimitsModal(false);
      fetchUsers(); // Refresh the list
      success('User limits updated successfully');
    } catch (err) {
      console.error('Error updating user limits:', err);
      showError('Failed to update user limits: ' + err.message);
    }
  };

  const columns = [
    {
      key: 'name',
      label: 'Name',
      render: (value, row) => (
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <FiUser className="w-4 h-4 text-blue-600" />
          </div>
          <div>
            <p className="font-medium text-gray-900">{value || 'Unknown'}</p>
            <p className="text-sm text-gray-500">{row.email}</p>
          </div>
        </div>
      )
    },
    {
      key: 'subscription.planName',
      label: 'Plan',
      type: 'badge',
      getBadgeClass: (value) => {
        switch (value) {
          case 'Pro':
            return 'bg-blue-100 text-blue-800';
          case 'Enterprise':
            return 'bg-purple-100 text-purple-800';
          default:
            return 'bg-gray-100 text-gray-800';
        }
      },
      render: (value, row) => {
        const planName = row.subscription?.planName || 'Starter';
        const badgeClass = planName === 'Pro' 
          ? 'bg-blue-100 text-blue-800'
          : planName === 'Enterprise'
          ? 'bg-purple-100 text-purple-800'
          : 'bg-gray-100 text-gray-800';
        
        return (
          <span className={`px-2 py-1 text-xs font-medium rounded-full ${badgeClass}`}>
            {planName}
          </span>
        );
      }
    },
    {
      key: 'isVerified',
      label: 'Status',
      type: 'badge',
      getBadgeClass: (value) => value ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800',
      render: (value) => (
        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
          value ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
        }`}>
          {value ? 'Verified' : 'Pending'}
        </span>
      )
    },
    {
      key: 'createdAt',
      label: 'Joined',
      type: 'date',
      render: (value) => new Date(value).toLocaleDateString()
    },
    {
      key: 'activityCount30Days',
      label: 'Activity (30d)',
      render: (value) => value || 0
    }
  ];

  const actionItems = [
    { label: 'View Details', icon: FiUser, action: 'view' },
    { label: 'Edit User', icon: FiSettings, action: 'edit' },
    { label: 'Manage Limits', icon: FiUpload, action: 'limits' },
    { label: 'Delete User', icon: FiAlertTriangle, action: 'delete', className: 'text-red-600' }
  ];

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="flex items-center gap-3">
          <FiAlertTriangle className="w-5 h-5 text-red-600" />
          <h3 className="text-lg font-medium text-red-800">Error Loading Users</h3>
        </div>
        <p className="mt-2 text-red-700">{error}</p>
        <button
          onClick={fetchUsers}
          className="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">User Management</h1>
          <p className="text-gray-600">Manage user accounts, subscriptions, and limits</p>
        </div>
        
        <div className="flex items-center gap-3">
          <button className="flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
            <FiDownload className="w-4 h-4" />
            Export
          </button>
          <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <FiPlus className="w-4 h-4" />
            Add User
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Plan Filter</label>
            <select
              value={filters.planFilter}
              onChange={(e) => setFilters(prev => ({ ...prev, planFilter: e.target.value, page: 1 }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Plans</option>
              <option value="Starter">Starter</option>
              <option value="Pro">Pro</option>
              <option value="Enterprise">Enterprise</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Status Filter</label>
            <select
              value={filters.statusFilter}
              onChange={(e) => setFilters(prev => ({ ...prev, statusFilter: e.target.value, page: 1 }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Status</option>
              <option value="verified">Verified</option>
              <option value="unverified">Unverified</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
        </div>
      </div>

      {/* Users Table */}
      <DataTable
        data={users}
        columns={columns}
        loading={loading}
        pagination={pagination}
        onPageChange={handlePageChange}
        onSearch={handleSearch}
        onRowAction={handleRowAction}
        searchPlaceholder="Search users by name or email..."
        actionItems={actionItems}
      />

      {/* User Details Modal */}
      <Modal
        isOpen={showUserModal}
        onClose={() => setShowUserModal(false)}
        title="User Details"
        size="lg"
      >
        {selectedUser && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3">Basic Information</h4>
                <div className="space-y-3">
                  <div>
                    <label className="text-xs text-gray-500">Name</label>
                    <p className="text-sm text-gray-900">{selectedUser.name || 'Not provided'}</p>
                  </div>
                  <div>
                    <label className="text-xs text-gray-500">Email</label>
                    <p className="text-sm text-gray-900">{selectedUser.email}</p>
                  </div>
                  <div>
                    <label className="text-xs text-gray-500">Status</label>
                    <p className="text-sm text-gray-900">
                      {selectedUser.isVerified ? 'Verified' : 'Pending Verification'}
                    </p>
                  </div>
                  <div>
                    <label className="text-xs text-gray-500">Joined</label>
                    <p className="text-sm text-gray-900">
                      {new Date(selectedUser.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </div>
              
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3">Subscription</h4>
                <div className="space-y-3">
                  <div>
                    <label className="text-xs text-gray-500">Plan</label>
                    <p className="text-sm text-gray-900">
                      {selectedUser.subscription?.planName || 'Starter'}
                    </p>
                  </div>
                  <div>
                    <label className="text-xs text-gray-500">Status</label>
                    <p className="text-sm text-gray-900">
                      {selectedUser.subscription?.status || 'active'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </Modal>

      {/* Limits Management Modal */}
      <Modal
        isOpen={showLimitsModal}
        onClose={() => setShowLimitsModal(false)}
        title="Manage User Limits"
        size="lg"
      >
        {selectedUser && (
          <LimitsForm
            user={selectedUser}
            onSave={updateUserLimits}
            onCancel={() => setShowLimitsModal(false)}
          />
        )}
      </Modal>
    </div>
  );
};

// Limits Form Component
const LimitsForm = ({ user, onSave, onCancel }) => {
  const [limits, setLimits] = useState({
    planName: user.subscription?.planName || 'Starter',
    freeTierUploadCount: user.subscription?.freeTierUploadCount || 0,
    freeTierMessageCount: user.subscription?.freeTierMessageCount || 0,
    freeTierBusinessPlanCount: user.subscription?.freeTierBusinessPlanCount || 0
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(user.id, limits);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Plan</label>
        <select
          value={limits.planName}
          onChange={(e) => setLimits(prev => ({ ...prev, planName: e.target.value }))}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="Starter">Starter</option>
          <option value="Pro">Pro</option>
          <option value="Enterprise">Enterprise</option>
        </select>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Upload Count</label>
          <input
            type="number"
            value={limits.freeTierUploadCount}
            onChange={(e) => setLimits(prev => ({ ...prev, freeTierUploadCount: parseInt(e.target.value) || 0 }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Message Count</label>
          <input
            type="number"
            value={limits.freeTierMessageCount}
            onChange={(e) => setLimits(prev => ({ ...prev, freeTierMessageCount: parseInt(e.target.value) || 0 }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      <div className="flex justify-end gap-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Save Changes
        </button>
      </div>
    </form>
  );
};

export default UserManagement;
