// controllers/dashboard/DashboardController.js
import User from '../../models/User.js';
import { Admin, SystemSettings, UserActivity } from '../../models/dashboard/index.js';

/**
 * @desc    Get dashboard overview statistics
 * @route   GET /api/dashboard/overview
 * @access  Private (Admin only)
 */
export const getDashboardOverview = async (req, res) => {
  try {
    const now = new Date();
    const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // Get user statistics
    const totalUsers = await User.countDocuments();
    const verifiedUsers = await User.countDocuments({ isVerified: true });
    const activeUsers = await User.countDocuments({ 
      'subscription.status': 'active' 
    });

    // Get subscription statistics
    const subscriptionStats = await User.aggregate([
      {
        $group: {
          _id: '$subscription.planName',
          count: { $sum: 1 }
        }
      }
    ]);

    // Get activity statistics for today
    const todayActivity = await UserActivity.aggregate([
      {
        $match: {
          createdAt: { $gte: startOfDay }
        }
      },
      {
        $group: {
          _id: '$activityType',
          count: { $sum: 1 }
        }
      }
    ]);

    // Get recent user registrations
    const recentRegistrations = await User.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select('name email createdAt subscription.planName')
      .lean();

    // Get system health metrics
    const errorCount = await UserActivity.countDocuments({
      success: false,
      createdAt: { $gte: startOfDay }
    });

    // Calculate growth metrics
    const usersThisMonth = await User.countDocuments({
      createdAt: { $gte: startOfMonth }
    });

    const usersLastMonth = await User.countDocuments({
      createdAt: { 
        $gte: new Date(startOfMonth.getFullYear(), startOfMonth.getMonth() - 1, 1),
        $lt: startOfMonth
      }
    });

    const growthRate = usersLastMonth > 0 
      ? ((usersThisMonth - usersLastMonth) / usersLastMonth * 100).toFixed(2)
      : 0;

    res.json({
      success: true,
      data: {
        userStats: {
          total: totalUsers,
          verified: verifiedUsers,
          active: activeUsers,
          unverified: totalUsers - verifiedUsers,
          growthRate: parseFloat(growthRate)
        },
        subscriptionStats: subscriptionStats.reduce((acc, stat) => {
          acc[stat._id || 'unknown'] = stat.count;
          return acc;
        }, {}),
        activityStats: {
          today: todayActivity.reduce((acc, activity) => {
            acc[activity._id] = activity.count;
            return acc;
          }, {}),
          totalToday: todayActivity.reduce((sum, activity) => sum + activity.count, 0)
        },
        systemHealth: {
          errorsToday: errorCount,
          status: errorCount < 10 ? 'healthy' : errorCount < 50 ? 'warning' : 'critical'
        },
        recentActivity: {
          registrations: recentRegistrations
        }
      }
    });

  } catch (error) {
    console.error('Error in getDashboardOverview:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Server error while fetching dashboard overview.' 
    });
  }
};

/**
 * @desc    Get system analytics data
 * @route   GET /api/dashboard/analytics
 * @access  Private (Admin only)
 */
export const getSystemAnalytics = async (req, res) => {
  try {
    const { period = '7d', startDate, endDate } = req.query;
    
    let start, end;
    const now = new Date();

    if (startDate && endDate) {
      start = new Date(startDate);
      end = new Date(endDate);
    } else {
      switch (period) {
        case '24h':
          start = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case '7d':
          start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '30d':
          start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        case '90d':
          start = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
          break;
        default:
          start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      }
      end = now;
    }

    // Get activity statistics
    const activityStats = await UserActivity.getActivityStats(start, end);
    
    // Get user engagement data
    const engagementData = await UserActivity.getUserEngagement(start, end);
    
    // Get error analysis
    const errorAnalysis = await UserActivity.getErrorAnalysis(start, end);

    // Get daily activity trends
    const dailyTrends = await UserActivity.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } },
            type: "$activityType"
          },
          count: { $sum: 1 }
        }
      },
      {
        $group: {
          _id: "$_id.date",
          activities: {
            $push: {
              type: "$_id.type",
              count: "$count"
            }
          },
          totalCount: { $sum: "$count" }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    res.json({
      success: true,
      data: {
        period: { start, end },
        activityStats,
        engagementData: engagementData.slice(0, 100), // Top 100 most engaged users
        errorAnalysis,
        dailyTrends
      }
    });

  } catch (error) {
    console.error('Error in getSystemAnalytics:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Server error while fetching analytics data.' 
    });
  }
};

/**
 * @desc    Get system performance metrics
 * @route   GET /api/dashboard/performance
 * @access  Private (Admin only)
 */
export const getPerformanceMetrics = async (req, res) => {
  try {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

    // Get recent performance data
    const performanceData = await UserActivity.aggregate([
      {
        $match: {
          createdAt: { $gte: oneHourAgo },
          'requestInfo.responseTime': { $exists: true }
        }
      },
      {
        $group: {
          _id: '$requestInfo.endpoint',
          avgResponseTime: { $avg: '$requestInfo.responseTime' },
          maxResponseTime: { $max: '$requestInfo.responseTime' },
          minResponseTime: { $min: '$requestInfo.responseTime' },
          requestCount: { $sum: 1 },
          errorCount: { $sum: { $cond: ['$success', 0, 1] } }
        }
      },
      {
        $project: {
          endpoint: '$_id',
          avgResponseTime: { $round: ['$avgResponseTime', 2] },
          maxResponseTime: 1,
          minResponseTime: 1,
          requestCount: 1,
          errorCount: 1,
          errorRate: { 
            $round: [
              { $multiply: [{ $divide: ['$errorCount', '$requestCount'] }, 100] }, 
              2
            ] 
          }
        }
      },
      { $sort: { requestCount: -1 } }
    ]);

    // Get overall system metrics
    const totalRequests = await UserActivity.countDocuments({
      createdAt: { $gte: oneHourAgo }
    });

    const totalErrors = await UserActivity.countDocuments({
      createdAt: { $gte: oneHourAgo },
      success: false
    });

    const overallErrorRate = totalRequests > 0 
      ? ((totalErrors / totalRequests) * 100).toFixed(2)
      : 0;

    res.json({
      success: true,
      data: {
        timeframe: 'Last 1 hour',
        overall: {
          totalRequests,
          totalErrors,
          errorRate: parseFloat(overallErrorRate),
          status: overallErrorRate < 1 ? 'excellent' : 
                  overallErrorRate < 5 ? 'good' : 
                  overallErrorRate < 10 ? 'warning' : 'critical'
        },
        endpoints: performanceData
      }
    });

  } catch (error) {
    console.error('Error in getPerformanceMetrics:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Server error while fetching performance metrics.' 
    });
  }
};
