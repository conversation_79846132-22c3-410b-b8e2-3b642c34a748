// src/dashboard/Dashboard.jsx
import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { DashboardLayout, ProtectedRoute } from './components';
import { DashboardOverview, UserManagement, AdminLogin } from './pages';
import { useAdminAuth } from './hooks';
import { PERMISSIONS } from './utils/constants';

const Dashboard = () => {
  const { currentAdmin, isAuthenticated, loading, logout } = useAdminAuth();

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!isAuthenticated || !currentAdmin) {
    return <AdminLogin />;
  }

  return (
    <DashboardLayout currentAdmin={currentAdmin} onLogout={logout}>
      <Routes>
        <Route
          path="/"
          element={
            <ProtectedRoute requiredPermission={PERMISSIONS.ANALYTICS}>
              <DashboardOverview />
            </ProtectedRoute>
          }
        />
        <Route
          path="/users"
          element={
            <ProtectedRoute requiredPermission={PERMISSIONS.USER_MANAGEMENT}>
              <UserManagement />
            </ProtectedRoute>
          }
        />
        <Route
          path="/analytics"
          element={
            <ProtectedRoute requiredPermission={PERMISSIONS.ANALYTICS}>
              <div>Analytics Page (Coming Soon)</div>
            </ProtectedRoute>
          }
        />
        <Route
          path="/settings"
          element={
            <ProtectedRoute requiredPermission={PERMISSIONS.SYSTEM_SETTINGS}>
              <div>Settings Page (Coming Soon)</div>
            </ProtectedRoute>
          }
        />
        <Route
          path="/admins"
          element={
            <ProtectedRoute requiredPermission={PERMISSIONS.ADMIN_MANAGEMENT}>
              <div>Admin Management Page (Coming Soon)</div>
            </ProtectedRoute>
          }
        />
        <Route path="*" element={<Navigate to="/dashboard" replace />} />
      </Routes>
    </DashboardLayout>
  );
};

export default Dashboard;
