// src/dashboard/utils/constants.js

/**
 * User plan constants
 */
export const USER_PLANS = {
  STARTER: 'Starter',
  PRO: 'Pro',
  ENTERPRISE: 'Enterprise'
};

/**
 * Admin roles
 */
export const ADMIN_ROLES = {
  SUPER_ADMIN: 'super_admin',
  ADMIN: 'admin',
  MODERATOR: 'moderator'
};

/**
 * User status constants
 */
export const USER_STATUS = {
  VERIFIED: 'verified',
  UNVERIFIED: 'unverified',
  ACTIVE: 'active',
  INACTIVE: 'inactive'
};

/**
 * System health status
 */
export const SYSTEM_HEALTH = {
  HEALTHY: 'healthy',
  WARNING: 'warning',
  CRITICAL: 'critical',
  UNKNOWN: 'unknown'
};

/**
 * Activity types
 */
export const ACTIVITY_TYPES = {
  LOGIN: 'login',
  LOGOUT: 'logout',
  REGISTRATION: 'registration',
  EMAIL_VERIFICATION: 'email_verification',
  PASSWORD_CHANGE: 'password_change',
  PROFILE_UPDATE: 'profile_update',
  SUBSCRIPTION_CHANGE: 'subscription_change',
  FILE_UPLOAD: 'file_upload',
  FILE_DOWNLOAD: 'file_download',
  AI_CHAT_MESSAGE: 'ai_chat_message',
  BUSINESS_PLAN_GENERATION: 'business_plan_generation',
  INVESTOR_PITCH_GENERATION: 'investor_pitch_generation',
  BUSINESS_QA_QUERY: 'business_qa_query',
  PDF_ANALYSIS: 'pdf_analysis',
  PAYMENT_INITIATED: 'payment_initiated',
  PAYMENT_COMPLETED: 'payment_completed',
  PAYMENT_FAILED: 'payment_failed',
  LIMIT_EXCEEDED: 'limit_exceeded',
  FEATURE_ACCESS: 'feature_access',
  ERROR_OCCURRED: 'error_occurred'
};

/**
 * Permission types
 */
export const PERMISSIONS = {
  USER_MANAGEMENT: 'userManagement',
  LIMITS_MANAGEMENT: 'limitsManagement',
  SYSTEM_SETTINGS: 'systemSettings',
  ANALYTICS: 'analytics',
  ADMIN_MANAGEMENT: 'adminManagement'
};

/**
 * Default pagination settings
 */
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 20,
  LIMIT_OPTIONS: [10, 20, 50, 100]
};

/**
 * Chart colors
 */
export const CHART_COLORS = {
  PRIMARY: '#3B82F6',
  SUCCESS: '#10B981',
  WARNING: '#F59E0B',
  DANGER: '#EF4444',
  INFO: '#06B6D4',
  PURPLE: '#8B5CF6',
  PINK: '#EC4899',
  INDIGO: '#6366F1'
};

/**
 * File upload constants
 */
export const FILE_UPLOAD = {
  MAX_SIZE_MB: 10,
  ALLOWED_TYPES: ['pdf', 'doc', 'docx', 'txt'],
  ALLOWED_IMAGE_TYPES: ['jpg', 'jpeg', 'png', 'gif', 'webp']
};

/**
 * API endpoints
 */
export const API_ENDPOINTS = {
  // Dashboard
  DASHBOARD_OVERVIEW: '/api/dashboard/overview',
  SYSTEM_ANALYTICS: '/api/dashboard/analytics',
  PERFORMANCE_METRICS: '/api/dashboard/performance',
  
  // Users
  USERS: '/api/dashboard/users',
  USER_DETAILS: (id) => `/api/dashboard/users/${id}`,
  USER_LIMITS: (id) => `/api/dashboard/users/${id}/limits`,
  BULK_UPDATE_USERS: '/api/dashboard/users/bulk-update',
  
  // Admins
  ADMIN_LOGIN: '/api/dashboard/admins/auth/login',
  ADMIN_ME: '/api/dashboard/admins/auth/me',
  ADMIN_CHANGE_PASSWORD: '/api/dashboard/admins/auth/change-password',
  ADMINS: '/api/dashboard/admins',
  ADMIN_DETAILS: (id) => `/api/dashboard/admins/${id}`,
  
  // Settings
  SYSTEM_SETTINGS: '/api/dashboard/settings',
  DEFAULT_LIMITS: (plan) => `/api/dashboard/settings/limits/${plan}`,
  SYSTEM_CONFIG: '/api/dashboard/settings/system',
  RATE_LIMITING: '/api/dashboard/settings/rate-limiting',
  SECURITY_SETTINGS: '/api/dashboard/settings/security',
  FEATURE_FLAGS: '/api/dashboard/settings/features',
  RESET_SETTINGS: '/api/dashboard/settings/reset',
  SETTINGS_HISTORY: '/api/dashboard/settings/history'
};

/**
 * Local storage keys
 */
export const STORAGE_KEYS = {
  ADMIN_TOKEN: 'adminToken',
  ADMIN_DATA: 'adminData',
  DASHBOARD_PREFERENCES: 'dashboardPreferences',
  THEME: 'dashboardTheme'
};

/**
 * Date format options
 */
export const DATE_FORMATS = {
  SHORT: 'short',
  LONG: 'long',
  TIME: 'time',
  DATETIME: 'datetime',
  RELATIVE: 'relative'
};

/**
 * Sort directions
 */
export const SORT_DIRECTIONS = {
  ASC: 'asc',
  DESC: 'desc'
};

/**
 * Modal sizes
 */
export const MODAL_SIZES = {
  SM: 'sm',
  MD: 'md',
  LG: 'lg',
  XL: 'xl'
};

/**
 * Loading spinner sizes
 */
export const SPINNER_SIZES = {
  SM: 'sm',
  MD: 'md',
  LG: 'lg',
  XL: 'xl'
};

/**
 * Toast types
 */
export const TOAST_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info'
};

/**
 * Default limits for different plans
 */
export const DEFAULT_LIMITS = {
  [USER_PLANS.STARTER]: {
    uploadLimit: 5,
    messageLimit: 50,
    businessPlanLimit: 2,
    investorPitchLimit: 1,
    businessQALimit: 10
  },
  [USER_PLANS.PRO]: {
    uploadLimit: 100,
    messageLimit: 1000,
    businessPlanLimit: 20,
    investorPitchLimit: 10,
    businessQALimit: 100
  },
  [USER_PLANS.ENTERPRISE]: {
    uploadLimit: -1, // Unlimited
    messageLimit: -1,
    businessPlanLimit: -1,
    investorPitchLimit: -1,
    businessQALimit: -1
  }
};

/**
 * Navigation items for dashboard
 */
export const NAVIGATION_ITEMS = [
  {
    name: 'Overview',
    href: '/dashboard',
    permission: PERMISSIONS.ANALYTICS
  },
  {
    name: 'User Management',
    href: '/dashboard/users',
    permission: PERMISSIONS.USER_MANAGEMENT
  },
  {
    name: 'Analytics',
    href: '/dashboard/analytics',
    permission: PERMISSIONS.ANALYTICS
  },
  {
    name: 'System Settings',
    href: '/dashboard/settings',
    permission: PERMISSIONS.SYSTEM_SETTINGS
  },
  {
    name: 'Admin Management',
    href: '/dashboard/admins',
    permission: PERMISSIONS.ADMIN_MANAGEMENT
  }
];

/**
 * Time periods for analytics
 */
export const TIME_PERIODS = {
  '24h': '24 Hours',
  '7d': '7 Days',
  '30d': '30 Days',
  '90d': '90 Days'
};

/**
 * Export formats
 */
export const EXPORT_FORMATS = {
  CSV: 'csv',
  JSON: 'json',
  PDF: 'pdf'
};

/**
 * Validation rules
 */
export const VALIDATION_RULES = {
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PASSWORD_MIN_LENGTH: 8,
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 50,
  PHONE_MIN_LENGTH: 10,
  PHONE_MAX_LENGTH: 15
};
