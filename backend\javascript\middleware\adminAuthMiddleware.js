// middleware/adminAuthMiddleware.js
import jwt from 'jsonwebtoken';
import { Admin } from '../models/dashboard/index.js';

/**
 * Protects admin routes by verifying a JWT token from the Authorization header.
 * If valid, it attaches the admin object (minus password) to the request object (req.admin).
 */
const protectAdmin = async (req, res, next) => {
    let token;

    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
        try {
            // Extract token from "Bearer <token>"
            token = req.headers.authorization.split(' ')[1];

            if (!process.env.JWT_SECRET) {
                console.error('CRITICAL SERVER ERROR: JWT_SECRET is not defined in environment variables!');
                return res.status(500).json({ 
                    success: false, 
                    error: 'Server configuration error: Unable to process authentication.' 
                });
            }

            // Verify the token
            const decoded = jwt.verify(token, process.env.JWT_SECRET);

            // Ensure the JWT payload has an 'id' field and is an admin token
            if (!decoded.id || decoded.type !== 'admin') {
                console.error('Authorization Error: Invalid admin token payload.', decoded);
                return res.status(401).json({ 
                    success: false, 
                    error: 'Not authorized, invalid admin token.' 
                });
            }

            // Fetch the admin from the database using the ID from the token
            req.admin = await Admin.findById(decoded.id).select('-password');

            if (!req.admin) {
                console.error('Authorization Error: Admin not found in database for ID from token:', decoded.id);
                return res.status(401).json({ 
                    success: false, 
                    error: 'Not authorized, admin associated with this token no longer exists.' 
                });
            }

            // Check if admin account is active
            if (!req.admin.isActive) {
                return res.status(403).json({ 
                    success: false, 
                    error: 'Admin account is deactivated.' 
                });
            }

            // Check if admin account is locked
            if (req.admin.isLocked) {
                return res.status(423).json({ 
                    success: false, 
                    error: 'Admin account is temporarily locked.' 
                });
            }

            // Admin is authenticated, proceed to the next middleware or route handler
            next();
        } catch (error) {
            console.error('Authentication error in protectAdmin middleware:', error.name, '-', error.message);
            if (error.name === 'JsonWebTokenError') {
                return res.status(401).json({ 
                    success: false, 
                    error: 'Not authorized, token is invalid or malformed.' 
                });
            }
            if (error.name === 'TokenExpiredError') {
                return res.status(401).json({ 
                    success: false, 
                    error: 'Not authorized, token has expired. Please log in again.' 
                });
            }
            // For other unexpected errors during token processing
            return res.status(401).json({ 
                success: false, 
                error: 'Not authorized, token processing failed.' 
            });
        }
    } else {
        return res.status(401).json({ 
            success: false, 
            error: 'Not authorized, no token provided or authentication header is malformed.' 
        });
    }
};

/**
 * Middleware to check if admin has specific permission
 */
const requirePermission = (permission) => {
    return (req, res, next) => {
        if (!req.admin) {
            return res.status(401).json({ 
                success: false, 
                error: 'Admin authentication required.' 
            });
        }

        if (!req.admin.hasPermission(permission)) {
            return res.status(403).json({ 
                success: false, 
                error: `Insufficient permissions. Required: ${permission}` 
            });
        }

        next();
    };
};

/**
 * Middleware to check if admin is super admin
 */
const requireSuperAdmin = (req, res, next) => {
    if (!req.admin) {
        return res.status(401).json({ 
            success: false, 
            error: 'Admin authentication required.' 
        });
    }

    if (req.admin.role !== 'super_admin') {
        return res.status(403).json({ 
            success: false, 
            error: 'Super admin access required.' 
        });
    }

    next();
};

/**
 * Middleware to check if admin has admin management permission (for admin CRUD operations)
 */
const requireAdminManagement = (req, res, next) => {
    if (!req.admin) {
        return res.status(401).json({ 
            success: false, 
            error: 'Admin authentication required.' 
        });
    }

    if (!req.admin.hasPermission('adminManagement')) {
        return res.status(403).json({ 
            success: false, 
            error: 'Admin management permission required.' 
        });
    }

    next();
};

export { 
    protectAdmin, 
    requirePermission, 
    requireSuperAdmin, 
    requireAdminManagement 
};
