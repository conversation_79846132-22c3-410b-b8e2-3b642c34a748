// src/dashboard/utils/formatters.js

/**
 * Format numbers with appropriate suffixes (K, M, B)
 */
export const formatNumber = (num) => {
  if (num === null || num === undefined) return '0';
  
  const number = Number(num);
  
  if (number >= 1000000000) {
    return (number / 1000000000).toFixed(1) + 'B';
  } else if (number >= 1000000) {
    return (number / 1000000).toFixed(1) + 'M';
  } else if (number >= 1000) {
    return (number / 1000).toFixed(1) + 'K';
  }
  
  return number.toLocaleString();
};

/**
 * Format currency values
 */
export const formatCurrency = (amount, currency = 'USD') => {
  if (amount === null || amount === undefined) return '$0.00';
  
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
};

/**
 * Format percentages
 */
export const formatPercentage = (value, decimals = 1) => {
  if (value === null || value === undefined) return '0%';
  
  return `${Number(value).toFixed(decimals)}%`;
};

/**
 * Format dates in various formats
 */
export const formatDate = (date, format = 'short') => {
  if (!date) return 'N/A';
  
  const dateObj = new Date(date);
  
  if (isNaN(dateObj.getTime())) return 'Invalid Date';
  
  switch (format) {
    case 'short':
      return dateObj.toLocaleDateString();
    case 'long':
      return dateObj.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    case 'time':
      return dateObj.toLocaleTimeString();
    case 'datetime':
      return dateObj.toLocaleString();
    case 'relative':
      return formatRelativeTime(dateObj);
    default:
      return dateObj.toLocaleDateString();
  }
};

/**
 * Format relative time (e.g., "2 hours ago")
 */
export const formatRelativeTime = (date) => {
  if (!date) return 'N/A';
  
  const now = new Date();
  const dateObj = new Date(date);
  const diffInSeconds = Math.floor((now - dateObj) / 1000);
  
  if (diffInSeconds < 60) {
    return 'Just now';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours} hour${hours > 1 ? 's' : ''} ago`;
  } else if (diffInSeconds < 2592000) {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days} day${days > 1 ? 's' : ''} ago`;
  } else if (diffInSeconds < 31536000) {
    const months = Math.floor(diffInSeconds / 2592000);
    return `${months} month${months > 1 ? 's' : ''} ago`;
  } else {
    const years = Math.floor(diffInSeconds / 31536000);
    return `${years} year${years > 1 ? 's' : ''} ago`;
  }
};

/**
 * Format file sizes
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Format duration in milliseconds to human readable format
 */
export const formatDuration = (milliseconds) => {
  if (!milliseconds) return '0ms';
  
  if (milliseconds < 1000) {
    return `${milliseconds}ms`;
  } else if (milliseconds < 60000) {
    return `${(milliseconds / 1000).toFixed(1)}s`;
  } else if (milliseconds < 3600000) {
    const minutes = Math.floor(milliseconds / 60000);
    const seconds = Math.floor((milliseconds % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  } else {
    const hours = Math.floor(milliseconds / 3600000);
    const minutes = Math.floor((milliseconds % 3600000) / 60000);
    return `${hours}h ${minutes}m`;
  }
};

/**
 * Format user plan names with proper capitalization
 */
export const formatPlanName = (plan) => {
  if (!plan) return 'Unknown';
  
  const planMap = {
    'starter': 'Starter',
    'pro': 'Pro',
    'enterprise': 'Enterprise',
    'free': 'Free',
    'basic': 'Basic',
    'premium': 'Premium'
  };
  
  return planMap[plan.toLowerCase()] || plan;
};

/**
 * Format user status
 */
export const formatUserStatus = (isVerified, isActive = true) => {
  if (!isActive) return 'Inactive';
  return isVerified ? 'Verified' : 'Pending';
};

/**
 * Format admin role
 */
export const formatAdminRole = (role) => {
  if (!role) return 'Unknown';
  
  const roleMap = {
    'super_admin': 'Super Admin',
    'admin': 'Admin',
    'moderator': 'Moderator'
  };
  
  return roleMap[role] || role;
};

/**
 * Format activity type for display
 */
export const formatActivityType = (activityType) => {
  if (!activityType) return 'Unknown';
  
  const activityMap = {
    'login': 'Login',
    'logout': 'Logout',
    'registration': 'Registration',
    'email_verification': 'Email Verification',
    'password_change': 'Password Change',
    'profile_update': 'Profile Update',
    'subscription_change': 'Subscription Change',
    'file_upload': 'File Upload',
    'file_download': 'File Download',
    'ai_chat_message': 'AI Chat Message',
    'business_plan_generation': 'Business Plan Generation',
    'investor_pitch_generation': 'Investor Pitch Generation',
    'business_qa_query': 'Business Q&A Query',
    'pdf_analysis': 'PDF Analysis',
    'payment_initiated': 'Payment Initiated',
    'payment_completed': 'Payment Completed',
    'payment_failed': 'Payment Failed',
    'limit_exceeded': 'Limit Exceeded',
    'feature_access': 'Feature Access',
    'error_occurred': 'Error Occurred'
  };
  
  return activityMap[activityType] || activityType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
};

/**
 * Format system health status
 */
export const formatSystemHealth = (status) => {
  const statusMap = {
    'healthy': 'Healthy',
    'warning': 'Warning',
    'critical': 'Critical',
    'unknown': 'Unknown'
  };
  
  return statusMap[status] || 'Unknown';
};

/**
 * Truncate text with ellipsis
 */
export const truncateText = (text, maxLength = 50) => {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

/**
 * Format email for display (hide part of email for privacy)
 */
export const formatEmailForDisplay = (email, showFull = false) => {
  if (!email || showFull) return email;
  
  const [localPart, domain] = email.split('@');
  if (!domain) return email;
  
  if (localPart.length <= 3) {
    return `${localPart[0]}***@${domain}`;
  }
  
  return `${localPart.substring(0, 2)}***@${domain}`;
};

/**
 * Format phone number
 */
export const formatPhoneNumber = (phone) => {
  if (!phone) return '';
  
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '');
  
  // Format as (XXX) XXX-XXXX for US numbers
  if (cleaned.length === 10) {
    return `(${cleaned.substring(0, 3)}) ${cleaned.substring(3, 6)}-${cleaned.substring(6)}`;
  }
  
  return phone;
};
