// routes/dashboard/adminRoutes.js
import express from 'express';
import {
  loginAdmin,
  getCurrentAdmin,
  createAdmin,
  getAllAdmins,
  updateAdmin,
  deleteAdmin,
  changeAdminPassword,
  registerFirstAdmin,
  getRegistrationStatus
} from '../../controllers/dashboard/index.js';
import { 
  protectAdmin, 
  requireSuperAdmin, 
  requireAdminManagement 
} from '../../middleware/adminAuthMiddleware.js';

const router = express.Router();

// Admin authentication routes
router.post('/auth/login', loginAdmin);
router.post('/register', registerFirstAdmin);
router.get('/registration-status', getRegistrationStatus);
router.get('/auth/me', protectAdmin, getCurrentAdmin);
router.put('/auth/change-password', protectAdmin, changeAdminPassword);

// Admin management routes (require admin management permission)
router.post('/', protectAdmin, requireAdminManagement, createAdmin);
router.get('/', protectAdmin, requireAdminManagement, getAllAdmins);
router.put('/:id', protectAdmin, requireAdminManagement, updateAdmin);
router.delete('/:id', protectAdmin, requireAdminManagement, deleteAdmin);

export default router;
