// src/dashboard/pages/DashboardOverview.jsx
import React, { useState, useEffect } from 'react';
import { 
  FiUsers, 
  FiUserCheck, 
  FiActivity, 
  FiTrendingUp,
  FiAlertTriangle,
  FiDollarSign
} from 'react-icons/fi';
import { StatsCard, Chart, LoadingSpinner } from '../components';

const DashboardOverview = () => {
  const [overviewData, setOverviewData] = useState(null);
  const [analyticsData, setAnalyticsData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch overview data
      const overviewResponse = await fetch(`http://localhost:3001/api/dashboard/overview`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      });

      if (!overviewResponse.ok) {
        throw new Error('Failed to fetch overview data');
      }

      const overviewResult = await overviewResponse.json();
      setOverviewData(overviewResult.data);

      // Fetch analytics data for charts
      const analyticsResponse = await fetch(`http://localhost:3001/api/dashboard/analytics?period=7d`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      });

      if (!analyticsResponse.ok) {
        throw new Error('Failed to fetch analytics data');
      }

      const analyticsResult = await analyticsResponse.json();
      setAnalyticsData(analyticsResult.data);
      
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <StatsCard key={i} loading={true} />
          ))}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Chart loading={true} />
          <Chart loading={true} />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="flex items-center gap-3">
          <FiAlertTriangle className="w-5 h-5 text-red-600" />
          <h3 className="text-lg font-medium text-red-800">Error Loading Dashboard</h3>
        </div>
        <p className="mt-2 text-red-700">{error}</p>
        <button
          onClick={fetchDashboardData}
          className="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  const { userStats, subscriptionStats, activityStats, systemHealth, recentActivity } = overviewData || {};

  // Prepare chart data
  const userGrowthData = analyticsData?.dailyTrends?.map(trend => ({
    date: trend._id,
    value: trend.totalCount
  })) || [];

  const subscriptionData = Object.entries(subscriptionStats || {}).map(([plan, count]) => ({
    plan,
    value: count
  }));

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Users"
          value={userStats?.total || 0}
          change={userStats?.growthRate}
          changeType={userStats?.growthRate > 0 ? 'positive' : 'negative'}
          icon={FiUsers}
          iconColor="text-blue-600"
          iconBgColor="bg-blue-100"
        />
        
        <StatsCard
          title="Verified Users"
          value={userStats?.verified || 0}
          subtitle={`${userStats?.unverified || 0} pending verification`}
          icon={FiUserCheck}
          iconColor="text-green-600"
          iconBgColor="bg-green-100"
        />
        
        <StatsCard
          title="Active Users"
          value={userStats?.active || 0}
          icon={FiActivity}
          iconColor="text-purple-600"
          iconBgColor="bg-purple-100"
        />
        
        <StatsCard
          title="Today's Activity"
          value={activityStats?.totalToday || 0}
          icon={FiTrendingUp}
          iconColor="text-orange-600"
          iconBgColor="bg-orange-100"
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Chart
          title="User Activity (Last 7 Days)"
          data={userGrowthData}
          type="line"
          xAxisKey="date"
          yAxisKey="value"
          color="#3B82F6"
        />
        
        <Chart
          title="Subscription Distribution"
          data={subscriptionData}
          type="bar"
          xAxisKey="plan"
          yAxisKey="value"
          color="#10B981"
        />
      </div>

      {/* System Health & Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* System Health */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">System Health</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Status</span>
              <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                systemHealth?.status === 'healthy' 
                  ? 'bg-green-100 text-green-800'
                  : systemHealth?.status === 'warning'
                  ? 'bg-yellow-100 text-yellow-800'
                  : 'bg-red-100 text-red-800'
              }`}>
                {systemHealth?.status || 'Unknown'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Errors Today</span>
              <span className="text-sm font-medium text-gray-900">
                {systemHealth?.errorsToday || 0}
              </span>
            </div>
          </div>
        </div>

        {/* Recent Registrations */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Registrations</h3>
          <div className="space-y-3">
            {recentActivity?.registrations?.length > 0 ? (
              recentActivity.registrations.map((user, index) => (
                <div key={index} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {user.name || 'Unknown'}
                    </p>
                    <p className="text-xs text-gray-500">{user.email}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-xs text-gray-500">
                      {new Date(user.createdAt).toLocaleDateString()}
                    </p>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      user.subscription?.planName === 'Pro' 
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {user.subscription?.planName || 'Starter'}
                    </span>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-sm text-gray-500">No recent registrations</p>
            )}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <FiUsers className="w-5 h-5 text-blue-600" />
            <div className="text-left">
              <p className="text-sm font-medium text-gray-900">Manage Users</p>
              <p className="text-xs text-gray-500">View and edit user accounts</p>
            </div>
          </button>
          
          <button className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <FiTrendingUp className="w-5 h-5 text-green-600" />
            <div className="text-left">
              <p className="text-sm font-medium text-gray-900">View Analytics</p>
              <p className="text-xs text-gray-500">Detailed system analytics</p>
            </div>
          </button>
          
          <button className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <FiDollarSign className="w-5 h-5 text-purple-600" />
            <div className="text-left">
              <p className="text-sm font-medium text-gray-900">System Settings</p>
              <p className="text-xs text-gray-500">Configure system parameters</p>
            </div>
          </button>
        </div>
      </div>
    </div>
  );
};

export default DashboardOverview;
