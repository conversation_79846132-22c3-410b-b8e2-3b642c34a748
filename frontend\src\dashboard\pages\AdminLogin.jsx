// src/dashboard/pages/AdminLogin.jsx
import React, { useState, useEffect } from 'react';
import { FiMail, FiLock, FiLogIn, FiAlertCircle, FiUser } from 'react-icons/fi';
import { LoadingSpinner } from '../components';
import { useAdminAuth } from '../hooks';

const AdminLogin = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [error, setError] = useState('');
  const [isRegistering, setIsRegistering] = useState(false);
  const [registrationAvailable, setRegistrationAvailable] = useState(false);
  const [checkingStatus, setCheckingStatus] = useState(true);
  const { login, loading } = useAdminAuth();

  useEffect(() => {
    checkRegistrationStatus();
  }, []);

  const checkRegistrationStatus = async () => {
    try {
      const response = await fetch('http://localhost:3001/api/dashboard/admins/registration-status');
      const result = await response.json();

      if (result.success) {
        setRegistrationAvailable(result.data.registrationAvailable);
        if (result.data.registrationAvailable) {
          setIsRegistering(true); // Show registration form if no admins exist
        }
      }
    } catch (err) {
      console.error('Error checking registration status:', err);
    } finally {
      setCheckingStatus(false);
    }
  };

  const handleRegister = async (e) => {
    e.preventDefault();
    setError('');

    if (!name || !email || !password) {
      setError('Please provide name, email, and password.');
      return;
    }

    try {
      const response = await fetch('http://localhost:3001/api/dashboard/admins/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ name, email, password })
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Registration failed');
      }

      // Store admin token and data
      localStorage.setItem('adminToken', result.data.token);
      localStorage.setItem('adminData', JSON.stringify(result.data.admin));

      // Refresh the page to trigger auth check
      window.location.reload();

    } catch (err) {
      console.error('Admin registration error:', err);
      setError(err.message || 'Registration failed');
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');

    if (!email || !password) {
      setError('Please provide both email and password.');
      return;
    }

    try {
      const result = await login(email, password);

      if (!result.success) {
        setError(result.error);
      }
      // If successful, the useAdminAuth hook will handle the redirect
    } catch (err) {
      console.error('Admin login error:', err);
      setError(err.message || 'Login failed');
    }
  };

  if (checkingStatus) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="lg" text="Checking system status..." />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Admin Dashboard
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            {isRegistering ? 'Create the first admin account' : 'Sign in to access the admin panel'}
          </p>
        </div>

        <form className="mt-8 space-y-6" onSubmit={isRegistering ? handleRegister : handleSubmit}>
          <div className="space-y-4">
            {/* Name Field (only for registration) */}
            {isRegistering && (
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  Full Name
                </label>
                <div className="relative">
                  <FiUser className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    id="name"
                    name="name"
                    type="text"
                    autoComplete="name"
                    required
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter your full name"
                  />
                </div>
              </div>
            )}

            {/* Email Field */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                Email Address
              </label>
              <div className="relative">
                <FiMail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder={isRegistering ? "Enter your email address" : "Enter your admin email"}
                />
              </div>
            </div>

            {/* Password Field */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                Password
              </label>
              <div className="relative">
                <FiLock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete={isRegistering ? "new-password" : "current-password"}
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder={isRegistering ? "Create a strong password" : "Enter your password"}
                />
              </div>
              {isRegistering && (
                <p className="mt-1 text-xs text-gray-500">
                  Password must be at least 8 characters with uppercase, lowercase, and numbers
                </p>
              )}
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <FiAlertCircle className="w-5 h-5 text-red-600 flex-shrink-0" />
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          )}

          {/* Submit Button */}
          <div>
            <button
              type="submit"
              disabled={loading}
              className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading ? (
                <LoadingSpinner size="sm" color="white" />
              ) : (
                <>
                  <FiLogIn className="w-5 h-5 mr-2" />
                  {isRegistering ? 'Create Admin Account' : 'Sign In'}
                </>
              )}
            </button>
          </div>

          {/* Toggle between login and registration */}
          {registrationAvailable && (
            <div className="text-center">
              <button
                type="button"
                onClick={() => setIsRegistering(!isRegistering)}
                className="text-sm text-blue-600 hover:text-blue-500 transition-colors"
              >
                {isRegistering ? 'Already have an account? Sign in' : 'Need to create the first admin? Register'}
              </button>
            </div>
          )}
        </form>

        {/* Security Notice */}
        <div className="mt-6 text-center">
          <p className="text-xs text-gray-500">
            {isRegistering
              ? 'This will create the first admin account with full system access.'
              : 'This is a secure admin area. All activities are logged and monitored.'
            }
          </p>
        </div>
      </div>
    </div>
  );
};

export default AdminLogin;
