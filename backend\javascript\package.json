{"name": "javascript", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1", "create-super-admin": "node scripts/createSuperAdmin.js", "dashboard:setup": "node scripts/createSuperAdmin.js <EMAIL> SuperAdmin123! 'Super Administrator'"}, "keywords": [], "type": "module", "author": "", "license": "ISC", "description": "", "dependencies": {"@google/generative-ai": "^0.24.1", "@lemonsqueezy/lemonsqueezy.js": "^4.0.0", "@paddle/paddle-node-sdk": "^2.7.3", "@paypal/checkout-server-sdk": "^1.0.3", "@sentry/node": "^9.30.0", "@socket.io/redis-adapter": "^8.3.0", "async-retry": "^1.3.3", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "bullmq": "^5.54.3", "compression": "^1.8.0", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.5.0", "express": "^5.1.0", "express-static-gzip": "^3.0.0", "form-data": "^4.0.2", "fs-extra": "^11.3.0", "helmet": "^8.1.0", "ioredis": "^5.6.1", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.0", "morgan": "^1.10.0", "multer": "^2.0.0", "node-cache": "^5.1.2", "node-fetch": "^3.3.2", "nodemailer": "^7.0.3", "nodemon": "^3.1.10", "pdf-parse": "^1.1.1", "redis": "^5.5.6", "shortid": "^2.2.17", "socket.io": "^4.8.1", "stripe": "^18.2.1", "uuid": "^11.1.0"}}