// src/pages/summaray/UploadPage.jsx
import React, { useEffect, useState, useCallback, useMemo, Suspense, lazy } from 'react';
import { FiLoader, FiAlertTriangle } from 'react-icons/fi';
import { Link } from 'react-router-dom';
import { useAuth } from '../../../../context/AuthContext';

// Import Hooks
import useOptimizedFileUpload from './common/hooks/useOptimizedFileUpload';
import { useStepManager } from './common/hooks/useStepManager';
import { useAnalysisAPI } from './common/hooks/useAnalysisAPI';
import { usePerformanceMonitor } from '../../../../components/Tools/Pdf/mindmap/utils/componentOptimization';

// Lazy Load Components
const StepRenderer = lazy(() => import('./common/ui/StepRenderer'));

// --- NEW: Import the new UploadLimit and ErrorMessages components ---
import UploadLimit from '../../../../common/messages/UploadLimit';
import ErrorMessages from '../../../../common/messages/ErrorMessages';

// Constants
const TOTAL_UPLOAD_STEPS = 3;
const NODEJS_SUMMARIZE_PDF_URL = 'http://localhost:3001/api/pdf/summarize';
const MAX_FILE_SIZE_MB_CONFIG = 15;
const FREE_TIER_PLAN_NAME = 'Starter';
const FREE_TIER_UPLOAD_LIMIT = 5;
const PRO_PLAN_NAME = 'Pro';
const PRO_TIER_UPLOAD_LIMIT = Number.MAX_SAFE_INTEGER; // Unlimited for Pro users

const ComponentLoader = () => (<div className="min-h-[420px] md:min-h-[480px] flex items-center justify-center"><FiLoader className="w-8 h-8 animate-spin text-sky-400" /></div>);

const PdfUploadPage = ({ openAuthPromptModal }) => {
  usePerformanceMonitor('PdfUploadPage');
  
  const { isAuthenticated, isLoading: authIsLoading, currentUser, token, incrementUploadCount, fetchCurrentUser } = useAuth();
  
  const [showLimitInfoPrompt, setShowLimitInfoPrompt] = useState(false);
  const [language, setLanguage] = useState('English');

  // Initialize Custom Hooks
  const fileUploader = useOptimizedFileUpload({ maxFileSizeMB: MAX_FILE_SIZE_MB_CONFIG });
  const api = useAnalysisAPI(NODEJS_SUMMARIZE_PDF_URL, isAuthenticated, token);
  const stepManager = useStepManager(1, TOTAL_UPLOAD_STEPS, () => {
    setShowLimitInfoPrompt(false);
    fileUploader.clearFileError();
    api.setApiErrors([]);
  });

  const { currentUploadCount, planUploadLimit, planNameForDisplay, userPlan } = useMemo(() => {
    const sub = currentUser?.subscription;
    if (!sub) return { currentUploadCount: 0, planUploadLimit: 0, planNameForDisplay: 'N/A', userPlan: null };
    const currentPlanName = sub.planName;
    if (currentPlanName === FREE_TIER_PLAN_NAME) return { currentUploadCount: sub.freeTierUploadCount ?? 0, planUploadLimit: FREE_TIER_UPLOAD_LIMIT, planNameForDisplay: FREE_TIER_PLAN_NAME, userPlan: currentPlanName };
    if (currentPlanName === PRO_PLAN_NAME) return { currentUploadCount: sub.proTierUploadCount ?? 0, planUploadLimit: PRO_TIER_UPLOAD_LIMIT, planNameForDisplay: PRO_PLAN_NAME, userPlan: currentPlanName };
    return { currentUploadCount: 0, planUploadLimit: Infinity, planNameForDisplay: sub.planName || 'N/A', userPlan: currentPlanName };
  }, [currentUser?.subscription]);

  const isAllowedToUpload = useCallback(() => {
    if (!isAuthenticated || !currentUser?.subscription) return false;
    if (planUploadLimit === Infinity) return true;
    // --- FIX: Corrected the typo from 'planUpload-limit' to 'planUploadLimit' ---
    return currentUploadCount < planUploadLimit;
  }, [isAuthenticated, currentUser?.subscription, currentUploadCount, planUploadLimit]);

  useEffect(() => {
    if (!authIsLoading && !isAuthenticated) openAuthPromptModal?.();
  }, [isAuthenticated, authIsLoading, openAuthPromptModal]);

  useEffect(() => {
    const shouldShowPrompt = isAuthenticated && !isAllowedToUpload();
    if (shouldShowPrompt !== showLimitInfoPrompt) setShowLimitInfoPrompt(shouldShowPrompt);
  }, [isAuthenticated, isAllowedToUpload, showLimitInfoPrompt]);

  const checkUploadPermission = useCallback(() => {
    if (!isAllowedToUpload()) {
      return false;
    }
    return true;
  }, [isAllowedToUpload]);

  const handleFileSelectedAndAdvance = useCallback((file) => {
    if (!file) return;
    if (!isAuthenticated) return openAuthPromptModal?.();
    api.setApiErrors([]);
    if (checkUploadPermission()) {
        fileUploader.selectFile(file);
        stepManager.handleNextStep();
    }
  }, [isAuthenticated, openAuthPromptModal, checkUploadPermission, api, fileUploader, stepManager]);

  const handleNextStepValidated = useCallback(() => {
    if (!isAuthenticated) return openAuthPromptModal?.();
    if (stepManager.currentStep === 1) {
      if (!fileUploader.selectedFile) {
        return; 
      }
      if (!checkUploadPermission()) return;
    }
    if (stepManager.currentStep === 2) {
      if (!language.trim()) return;
    }
    stepManager.handleNextStep();
  }, [isAuthenticated, fileUploader.selectedFile, language, openAuthPromptModal, stepManager, checkUploadPermission]);

  const handleAnalyseClick = async () => {
    if (!isAuthenticated || !fileUploader.selectedFile || !checkUploadPermission()) return;
    
    const submissionResult = await api.submitForAnalysis({
      selectedFile: fileUploader.selectedFile,
      language: language,
    });

    if (submissionResult?.success && (userPlan === FREE_TIER_PLAN_NAME || userPlan === PRO_PLAN_NAME)) {
      await incrementUploadCount();
      fetchCurrentUser?.();
    }
  };
  
  if (authIsLoading) {
    return (
        <div className="min-h-[calc(100vh-10rem)] flex items-center justify-center p-4 text-slate-300">
            <FiLoader className="w-10 h-10 animate-spin text-sky-400 mr-3" />
            Loading User Profile...
        </div>
    );
  }
  
  if (!isAuthenticated && !authIsLoading) {
    return (
        <div className="p-4 md:p-6 flex items-center justify-center min-h-[calc(100vh-10rem)]">
            <div className="w-full max-w-lg text-center p-8 border-2 border-dashed border-slate-600 rounded-lg">
                <FiAlertTriangle className="mx-auto h-12 w-12 text-yellow-400 mb-4" />
                <h3 className="text-xl font-semibold text-slate-100 mb-2">Authentication Required</h3>
                <p className="text-slate-300 mb-6">
                    Please <button onClick={() => openAuthPromptModal?.()} className="font-semibold text-sky-400 hover:text-sky-300 underline">sign in or create an account</button> to analyze your PDFs.
                </p>
            </div>
        </div>
    );
  }

  const disableFileUploadInteraction = !isAllowedToUpload();

  return (
    <div className="p-3 sm:p-4 md:p-6">
      {/* --- RENDER THE ERROR MESSAGES COMPONENT HERE --- */}
      <ErrorMessages 
        errors={api.apiErrors}
        onClearErrors={() => api.setApiErrors([])}
      />
      {/* --- END OF ERROR MESSAGES COMPONENT --- */}

      <div className="w-full max-w-4xl lg:max-w-5xl mx-auto p-4 sm:p-6">
        {showLimitInfoPrompt && (
          <UploadLimit
            planName={planNameForDisplay}
            currentUploads={currentUploadCount}
            uploadLimit={planUploadLimit}
          />
        )}
        <div className="min-h-[600px] flex flex-col justify-between mt-4 sm:mt-6">
          <Suspense fallback={<ComponentLoader />}>
            <StepRenderer
              currentStep={stepManager.currentStep}
              onNext={handleNextStepValidated}
              onBack={stepManager.handlePrevStep}
              selectedFile={fileUploader.selectedFile}
              removeFile={fileUploader.removeFile}
              maxFileSizeMB={MAX_FILE_SIZE_MB_CONFIG}
              disableUpload={disableFileUploadInteraction && !fileUploader.selectedFile}
              inputRef={fileUploader.inputRef}
              dragActive={fileUploader.dragActive}
              handleDrag={fileUploader.handleDrag}
              handleDrop={(e) => { const file = fileUploader.handleDrop(e); handleFileSelectedAndAdvance(file); }}
              handleChangeFile={(e) => { const file = fileUploader.handleChange(e); handleFileSelectedAndAdvance(file); }}
              language={language}
              setLanguage={setLanguage}
              onEditDetails={() => stepManager.goToStep(1)}
              handleAnalyse={handleAnalyseClick}
              isLoadingAPI={api.isLoadingAPI}
              estimatedTime="1-2 minutes"
            />
          </Suspense>
        </div>
        <p className="mt-8 text-center text-xs sm:text-sm text-slate-500">PDF Analysis © {new Date().getFullYear()} DOSKY</p>
      </div>
    </div>
  );
};

export default React.memo(PdfUploadPage);