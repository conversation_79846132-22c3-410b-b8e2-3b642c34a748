// src/dashboard/hooks/useAdminAuth.js
import { useState, useEffect, useCallback } from 'react';
import useDashboardAPI from './useDashboardAPI';

const useAdminAuth = () => {
  const [currentAdmin, setCurrentAdmin] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  const { getCurrentAdmin, loginAdmin } = useDashboardAPI();

  // Check if admin is authenticated on mount
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('adminToken');
      if (!token) {
        setLoading(false);
        return;
      }

      // Verify token with server
      const result = await getCurrentAdmin();
      setCurrentAdmin(result.data.admin);
      setIsAuthenticated(true);
    } catch (err) {
      console.error('Auth check failed:', err);
      // Clear invalid token
      localStorage.removeItem('adminToken');
      localStorage.removeItem('adminData');
      setCurrentAdmin(null);
      setIsAuthenticated(false);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [getCurrentAdmin]);

  const login = useCallback(async (email, password) => {
    try {
      setError(null);
      const result = await loginAdmin(email, password);
      
      // Store token and admin data
      localStorage.setItem('adminToken', result.data.token);
      localStorage.setItem('adminData', JSON.stringify(result.data.admin));
      
      setCurrentAdmin(result.data.admin);
      setIsAuthenticated(true);
      
      return { success: true, admin: result.data.admin };
    } catch (err) {
      setError(err.message);
      return { success: false, error: err.message };
    }
  }, [loginAdmin]);

  const logout = useCallback(() => {
    localStorage.removeItem('adminToken');
    localStorage.removeItem('adminData');
    setCurrentAdmin(null);
    setIsAuthenticated(false);
    setError(null);
  }, []);

  const hasPermission = useCallback((permission) => {
    if (!currentAdmin) return false;
    if (currentAdmin.role === 'super_admin') return true;
    return currentAdmin.permissions?.[permission] || false;
  }, [currentAdmin]);

  const isSuperAdmin = useCallback(() => {
    return currentAdmin?.role === 'super_admin';
  }, [currentAdmin]);

  const refreshAdmin = useCallback(async () => {
    try {
      const result = await getCurrentAdmin();
      setCurrentAdmin(result.data.admin);
      localStorage.setItem('adminData', JSON.stringify(result.data.admin));
      return result.data.admin;
    } catch (err) {
      console.error('Failed to refresh admin data:', err);
      throw err;
    }
  }, [getCurrentAdmin]);

  return {
    currentAdmin,
    isAuthenticated,
    loading,
    error,
    login,
    logout,
    hasPermission,
    isSuperAdmin,
    refreshAdmin,
    checkAuthStatus
  };
};

export default useAdminAuth;
