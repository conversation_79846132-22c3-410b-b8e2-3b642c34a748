// src/dashboard/hooks/useFilters.js
import { useState, useCallback, useMemo } from 'react';

const useFilters = (initialFilters = {}) => {
  const [filters, setFilters] = useState(initialFilters);
  const [searchTerm, setSearchTerm] = useState('');

  const updateFilter = useCallback((key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  const updateFilters = useCallback((newFilters) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters
    }));
  }, []);

  const removeFilter = useCallback((key) => {
    setFilters(prev => {
      const newFilters = { ...prev };
      delete newFilters[key];
      return newFilters;
    });
  }, []);

  const clearFilters = useCallback(() => {
    setFilters(initialFilters);
    setSearchTerm('');
  }, [initialFilters]);

  const clearFilter = useCallback((key) => {
    setFilters(prev => ({
      ...prev,
      [key]: initialFilters[key] || ''
    }));
  }, [initialFilters]);

  const updateSearch = useCallback((term) => {
    setSearchTerm(term);
  }, []);

  const hasActiveFilters = useMemo(() => {
    const hasFilters = Object.values(filters).some(value => 
      value !== '' && value !== null && value !== undefined
    );
    const hasSearch = searchTerm.trim() !== '';
    return hasFilters || hasSearch;
  }, [filters, searchTerm]);

  const activeFilterCount = useMemo(() => {
    const filterCount = Object.values(filters).filter(value => 
      value !== '' && value !== null && value !== undefined
    ).length;
    const searchCount = searchTerm.trim() !== '' ? 1 : 0;
    return filterCount + searchCount;
  }, [filters, searchTerm]);

  const getQueryParams = useCallback(() => {
    const params = new URLSearchParams();
    
    // Add search term
    if (searchTerm.trim()) {
      params.append('search', searchTerm.trim());
    }
    
    // Add filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== '' && value !== null && value !== undefined) {
        params.append(key, value);
      }
    });
    
    return params;
  }, [filters, searchTerm]);

  const getFilterObject = useCallback(() => {
    const filterObj = {};
    
    // Add search term
    if (searchTerm.trim()) {
      filterObj.search = searchTerm.trim();
    }
    
    // Add filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== '' && value !== null && value !== undefined) {
        filterObj[key] = value;
      }
    });
    
    return filterObj;
  }, [filters, searchTerm]);

  const setFromQueryParams = useCallback((searchParams) => {
    const newFilters = { ...initialFilters };
    const search = searchParams.get('search') || '';
    
    // Extract filters from search params
    for (const [key, value] of searchParams.entries()) {
      if (key !== 'search') {
        newFilters[key] = value;
      }
    }
    
    setFilters(newFilters);
    setSearchTerm(search);
  }, [initialFilters]);

  const createFilterToggle = useCallback((key, value) => {
    return () => {
      setFilters(prev => ({
        ...prev,
        [key]: prev[key] === value ? '' : value
      }));
    };
  }, []);

  const createMultiSelectFilter = useCallback((key) => {
    const addValue = (value) => {
      setFilters(prev => {
        const currentValues = prev[key] ? prev[key].split(',') : [];
        if (!currentValues.includes(value)) {
          return {
            ...prev,
            [key]: [...currentValues, value].join(',')
          };
        }
        return prev;
      });
    };

    const removeValue = (value) => {
      setFilters(prev => {
        const currentValues = prev[key] ? prev[key].split(',') : [];
        const newValues = currentValues.filter(v => v !== value);
        return {
          ...prev,
          [key]: newValues.length > 0 ? newValues.join(',') : ''
        };
      });
    };

    const toggleValue = (value) => {
      const currentValues = filters[key] ? filters[key].split(',') : [];
      if (currentValues.includes(value)) {
        removeValue(value);
      } else {
        addValue(value);
      }
    };

    const getValues = () => {
      return filters[key] ? filters[key].split(',') : [];
    };

    const hasValue = (value) => {
      const currentValues = filters[key] ? filters[key].split(',') : [];
      return currentValues.includes(value);
    };

    return {
      addValue,
      removeValue,
      toggleValue,
      getValues,
      hasValue
    };
  }, [filters]);

  return {
    // State
    filters,
    searchTerm,
    hasActiveFilters,
    activeFilterCount,
    
    // Actions
    updateFilter,
    updateFilters,
    removeFilter,
    clearFilters,
    clearFilter,
    updateSearch,
    
    // Utilities
    getQueryParams,
    getFilterObject,
    setFromQueryParams,
    createFilterToggle,
    createMultiSelectFilter
  };
};

export default useFilters;
