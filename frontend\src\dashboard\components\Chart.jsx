// src/dashboard/components/Chart.jsx
import React from 'react';

const Chart = ({ 
  type = 'line', 
  data = [], 
  title, 
  height = 300,
  loading = false,
  xAxisKey = 'date',
  yAxisKey = 'value',
  color = '#3B82F6'
}) => {
  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          {title && <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>}
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            <div className="h-4 bg-gray-200 rounded w-4/6"></div>
            <div className="h-4 bg-gray-200 rounded w-3/6"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        {title && <h3 className="text-lg font-medium text-gray-900 mb-4">{title}</h3>}
        <div className="flex items-center justify-center h-64 text-gray-500">
          No data available
        </div>
      </div>
    );
  }

  // Simple bar chart implementation
  const renderBarChart = () => {
    const maxValue = Math.max(...data.map(item => item[yAxisKey]));
    
    return (
      <div className="space-y-2">
        {data.map((item, index) => {
          const percentage = (item[yAxisKey] / maxValue) * 100;
          return (
            <div key={index} className="flex items-center gap-3">
              <div className="w-20 text-sm text-gray-600 truncate">
                {item[xAxisKey]}
              </div>
              <div className="flex-1 bg-gray-200 rounded-full h-6 relative">
                <div
                  className="h-full rounded-full transition-all duration-300"
                  style={{
                    width: `${percentage}%`,
                    backgroundColor: color
                  }}
                />
                <span className="absolute inset-0 flex items-center justify-center text-xs font-medium text-white">
                  {item[yAxisKey]}
                </span>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  // Simple line chart implementation using SVG
  const renderLineChart = () => {
    const maxValue = Math.max(...data.map(item => item[yAxisKey]));
    const minValue = Math.min(...data.map(item => item[yAxisKey]));
    const range = maxValue - minValue || 1;
    
    const width = 600;
    const chartHeight = height - 60; // Leave space for labels
    const padding = 40;
    
    const points = data.map((item, index) => {
      const x = padding + (index * (width - 2 * padding)) / (data.length - 1);
      const y = chartHeight - padding - ((item[yAxisKey] - minValue) / range) * (chartHeight - 2 * padding);
      return `${x},${y}`;
    }).join(' ');

    return (
      <div className="w-full overflow-x-auto">
        <svg width={width} height={height} className="min-w-full">
          {/* Grid lines */}
          {[0, 0.25, 0.5, 0.75, 1].map((ratio, index) => {
            const y = chartHeight - padding - (ratio * (chartHeight - 2 * padding));
            return (
              <g key={index}>
                <line
                  x1={padding}
                  y1={y}
                  x2={width - padding}
                  y2={y}
                  stroke="#E5E7EB"
                  strokeWidth="1"
                />
                <text
                  x={padding - 10}
                  y={y + 4}
                  textAnchor="end"
                  className="text-xs fill-gray-500"
                >
                  {Math.round(minValue + (ratio * range))}
                </text>
              </g>
            );
          })}
          
          {/* Line */}
          <polyline
            points={points}
            fill="none"
            stroke={color}
            strokeWidth="2"
            className="drop-shadow-sm"
          />
          
          {/* Data points */}
          {data.map((item, index) => {
            const x = padding + (index * (width - 2 * padding)) / (data.length - 1);
            const y = chartHeight - padding - ((item[yAxisKey] - minValue) / range) * (chartHeight - 2 * padding);
            return (
              <circle
                key={index}
                cx={x}
                cy={y}
                r="4"
                fill={color}
                className="drop-shadow-sm"
              />
            );
          })}
          
          {/* X-axis labels */}
          {data.map((item, index) => {
            const x = padding + (index * (width - 2 * padding)) / (data.length - 1);
            return (
              <text
                key={index}
                x={x}
                y={height - 10}
                textAnchor="middle"
                className="text-xs fill-gray-500"
              >
                {typeof item[xAxisKey] === 'string' ? item[xAxisKey].slice(0, 10) : item[xAxisKey]}
              </text>
            );
          })}
        </svg>
      </div>
    );
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      {title && <h3 className="text-lg font-medium text-gray-900 mb-4">{title}</h3>}
      <div style={{ height: `${height}px` }}>
        {type === 'bar' ? renderBarChart() : renderLineChart()}
      </div>
    </div>
  );
};

export default Chart;
