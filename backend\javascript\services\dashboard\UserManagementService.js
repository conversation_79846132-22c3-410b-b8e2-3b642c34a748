// services/dashboard/UserManagementService.js
import User from '../../models/User.js';
import { UserActivity, SystemSettings } from '../../models/dashboard/index.js';

/**
 * Service for handling user management operations
 */
class UserManagementService {

  /**
   * Apply default limits to a user based on their plan
   */
  static async applyDefaultLimits(userId, planName) {
    try {
      const settings = await SystemSettings.getCurrentSettings();
      const defaultLimits = settings.defaultLimits[planName.toLowerCase()];
      
      if (!defaultLimits) {
        throw new Error(`Invalid plan name: ${planName}`);
      }

      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Reset usage counts and apply new limits based on plan
      const updates = {
        'subscription.planName': planName,
        'subscription.status': 'active'
      };

      // Reset all usage counts when changing plans
      const usageFields = [
        'freeTierUploadCount',
        'freeTierBusinessPlanCount', 
        'freeTierInvestorPitchCount',
        'freeTierBusinessQACount',
        'freeTierMessageCount',
        'proTierUploadCount',
        'proTierBusinessPlanCount',
        'proTierInvestorPitchCount', 
        'proTierBusinessQACount',
        'proTierMessageCount'
      ];

      usageFields.forEach(field => {
        updates[`subscription.${field}`] = 0;
      });

      // Reset periodic reset dates
      updates['subscription.businessPlanMonthlyReset'] = new Date();
      updates['subscription.investorPitchMonthlyReset'] = new Date();
      updates['subscription.businessQADailyReset'] = new Date();

      await User.findByIdAndUpdate(userId, { $set: updates });

      return {
        success: true,
        message: `User plan updated to ${planName} with default limits applied`,
        limits: defaultLimits
      };
    } catch (error) {
      console.error('Error in applyDefaultLimits:', error);
      throw error;
    }
  }

  /**
   * Bulk update user plans
   */
  static async bulkUpdateUserPlans(userIds, newPlan, adminId) {
    try {
      const settings = await SystemSettings.getCurrentSettings();
      const defaultLimits = settings.defaultLimits[newPlan.toLowerCase()];
      
      if (!defaultLimits) {
        throw new Error(`Invalid plan name: ${newPlan}`);
      }

      const updates = {
        'subscription.planName': newPlan,
        'subscription.status': 'active'
      };

      // Reset usage counts
      const usageFields = [
        'freeTierUploadCount',
        'freeTierBusinessPlanCount',
        'freeTierInvestorPitchCount', 
        'freeTierBusinessQACount',
        'freeTierMessageCount',
        'proTierUploadCount',
        'proTierBusinessPlanCount',
        'proTierInvestorPitchCount',
        'proTierBusinessQACount',
        'proTierMessageCount'
      ];

      usageFields.forEach(field => {
        updates[`subscription.${field}`] = 0;
      });

      const result = await User.updateMany(
        { _id: { $in: userIds } },
        { $set: updates }
      );

      // Log the bulk update activity
      await UserActivity.create({
        userId: null,
        activityType: 'bulk_plan_update',
        metadata: {
          updatedBy: adminId,
          affectedUsers: userIds.length,
          newPlan: newPlan,
          matchedCount: result.matchedCount,
          modifiedCount: result.modifiedCount
        },
        success: true
      });

      return {
        success: true,
        message: `${result.modifiedCount} users updated to ${newPlan} plan`,
        matchedCount: result.matchedCount,
        modifiedCount: result.modifiedCount
      };
    } catch (error) {
      console.error('Error in bulkUpdateUserPlans:', error);
      throw error;
    }
  }

  /**
   * Get user usage statistics
   */
  static async getUserUsageStats(userId, period = '30d') {
    try {
      const now = new Date();
      let startDate;

      switch (period) {
        case '24h':
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '30d':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        case '90d':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      }

      const usageStats = await UserActivity.aggregate([
        {
          $match: {
            userId: userId,
            createdAt: { $gte: startDate, $lte: now }
          }
        },
        {
          $group: {
            _id: '$activityType',
            count: { $sum: 1 },
            lastActivity: { $max: '$createdAt' },
            avgProcessingTime: { $avg: '$metadata.processingTime' },
            successRate: { $avg: { $cond: ['$success', 1, 0] } }
          }
        },
        {
          $project: {
            activityType: '$_id',
            count: 1,
            lastActivity: 1,
            avgProcessingTime: { $round: ['$avgProcessingTime', 2] },
            successRate: { $multiply: ['$successRate', 100] }
          }
        },
        { $sort: { count: -1 } }
      ]);

      // Get daily activity breakdown
      const dailyActivity = await UserActivity.aggregate([
        {
          $match: {
            userId: userId,
            createdAt: { $gte: startDate, $lte: now }
          }
        },
        {
          $group: {
            _id: { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } },
            totalActivities: { $sum: 1 },
            uniqueFeatures: { $addToSet: '$activityType' }
          }
        },
        {
          $project: {
            date: '$_id',
            totalActivities: 1,
            uniqueFeaturesCount: { $size: '$uniqueFeatures' }
          }
        },
        { $sort: { date: 1 } }
      ]);

      return {
        period,
        overview: usageStats,
        dailyBreakdown: dailyActivity,
        totalActivities: usageStats.reduce((sum, stat) => sum + stat.count, 0)
      };
    } catch (error) {
      console.error('Error in getUserUsageStats:', error);
      throw error;
    }
  }

  /**
   * Check if user is approaching limits
   */
  static async checkUserLimits(userId) {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      const settings = await SystemSettings.getCurrentSettings();
      const planLimits = settings.defaultLimits[user.subscription.planName?.toLowerCase() || 'starter'];
      
      const warnings = [];
      const subscription = user.subscription;

      // Check each limit type
      const limitChecks = [
        {
          type: 'upload',
          current: subscription.freeTierUploadCount || 0,
          limit: planLimits.uploadLimit,
          field: 'Upload'
        },
        {
          type: 'message',
          current: subscription.freeTierMessageCount || 0,
          limit: planLimits.messageLimit,
          field: 'Message'
        },
        {
          type: 'businessPlan',
          current: subscription.freeTierBusinessPlanCount || 0,
          limit: planLimits.businessPlanLimit,
          field: 'Business Plan'
        },
        {
          type: 'investorPitch',
          current: subscription.freeTierInvestorPitchCount || 0,
          limit: planLimits.investorPitchLimit,
          field: 'Investor Pitch'
        },
        {
          type: 'businessQA',
          current: subscription.freeTierBusinessQACount || 0,
          limit: planLimits.businessQALimit,
          field: 'Business Q&A'
        }
      ];

      limitChecks.forEach(check => {
        if (check.limit > 0) { // -1 means unlimited
          const usagePercentage = (check.current / check.limit) * 100;
          
          if (usagePercentage >= 90) {
            warnings.push({
              type: check.type,
              field: check.field,
              current: check.current,
              limit: check.limit,
              usagePercentage: Math.round(usagePercentage),
              severity: 'critical'
            });
          } else if (usagePercentage >= 75) {
            warnings.push({
              type: check.type,
              field: check.field,
              current: check.current,
              limit: check.limit,
              usagePercentage: Math.round(usagePercentage),
              severity: 'warning'
            });
          }
        }
      });

      return {
        userId,
        planName: user.subscription.planName,
        limits: limitChecks,
        warnings,
        hasWarnings: warnings.length > 0
      };
    } catch (error) {
      console.error('Error in checkUserLimits:', error);
      throw error;
    }
  }

  /**
   * Reset user usage counts
   */
  static async resetUserUsage(userId, resetType = 'all', adminId) {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      const updates = {};

      if (resetType === 'all' || resetType === 'free') {
        updates['subscription.freeTierUploadCount'] = 0;
        updates['subscription.freeTierBusinessPlanCount'] = 0;
        updates['subscription.freeTierInvestorPitchCount'] = 0;
        updates['subscription.freeTierBusinessQACount'] = 0;
        updates['subscription.freeTierMessageCount'] = 0;
      }

      if (resetType === 'all' || resetType === 'pro') {
        updates['subscription.proTierUploadCount'] = 0;
        updates['subscription.proTierBusinessPlanCount'] = 0;
        updates['subscription.proTierInvestorPitchCount'] = 0;
        updates['subscription.proTierBusinessQACount'] = 0;
        updates['subscription.proTierMessageCount'] = 0;
      }

      if (resetType === 'all' || resetType === 'dates') {
        updates['subscription.businessPlanMonthlyReset'] = new Date();
        updates['subscription.investorPitchMonthlyReset'] = new Date();
        updates['subscription.businessQADailyReset'] = new Date();
      }

      await User.findByIdAndUpdate(userId, { $set: updates });

      // Log the reset activity
      await UserActivity.create({
        userId: userId,
        activityType: 'usage_reset',
        metadata: {
          resetBy: adminId,
          resetType: resetType,
          resetFields: Object.keys(updates)
        },
        success: true
      });

      return {
        success: true,
        message: `User usage counts reset successfully (${resetType})`,
        resetFields: Object.keys(updates)
      };
    } catch (error) {
      console.error('Error in resetUserUsage:', error);
      throw error;
    }
  }

  /**
   * Get users approaching limits
   */
  static async getUsersApproachingLimits(threshold = 80) {
    try {
      const users = await User.find({ isVerified: true }).lean();
      const settings = await SystemSettings.getCurrentSettings();
      
      const usersAtRisk = [];

      for (const user of users) {
        const planLimits = settings.defaultLimits[user.subscription.planName?.toLowerCase() || 'starter'];
        const subscription = user.subscription;
        
        const limitChecks = [
          {
            type: 'upload',
            current: subscription.freeTierUploadCount || 0,
            limit: planLimits.uploadLimit
          },
          {
            type: 'message', 
            current: subscription.freeTierMessageCount || 0,
            limit: planLimits.messageLimit
          },
          {
            type: 'businessPlan',
            current: subscription.freeTierBusinessPlanCount || 0,
            limit: planLimits.businessPlanLimit
          }
        ];

        const warnings = limitChecks.filter(check => {
          if (check.limit <= 0) return false; // Unlimited
          return (check.current / check.limit) * 100 >= threshold;
        });

        if (warnings.length > 0) {
          usersAtRisk.push({
            userId: user._id,
            email: user.email,
            name: user.name,
            planName: user.subscription.planName,
            warnings: warnings.map(w => ({
              type: w.type,
              usagePercentage: Math.round((w.current / w.limit) * 100),
              current: w.current,
              limit: w.limit
            }))
          });
        }
      }

      return usersAtRisk;
    } catch (error) {
      console.error('Error in getUsersApproachingLimits:', error);
      throw error;
    }
  }
}

export default UserManagementService;
