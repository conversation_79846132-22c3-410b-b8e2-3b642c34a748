// routes/dashboard/systemSettingsRoutes.js
import express from 'express';
import {
  getSystemSettings,
  updateSystemSettings,
  updateDefaultLimits,
  updateSystemConfig,
  updateRateLimiting,
  updateSecuritySettings,
  updateFeatureFlags,
  resetToDefaults,
  getSettingsHistory
} from '../../controllers/dashboard/index.js';
import { 
  protectAdmin, 
  requirePermission, 
  requireSuperAdmin 
} from '../../middleware/adminAuthMiddleware.js';

const router = express.Router();

// System settings routes
router.get('/', protectAdmin, requirePermission('systemSettings'), getSystemSettings);
router.put('/', protectAdmin, requireSuperAdmin, updateSystemSettings);
router.get('/history', protectAdmin, requirePermission('systemSettings'), getSettingsHistory);
router.post('/reset', protectAdmin, requireSuperAdmin, resetToDefaults);

// Specific settings categories (all require super admin)
router.put('/limits/:plan', protectAdmin, requireSuperAdmin, updateDefaultLimits);
router.put('/system', protectAdmin, requireSuperAdmin, updateSystemConfig);
router.put('/rate-limiting', protectAdmin, requireSuperAdmin, updateRateLimiting);
router.put('/security', protectAdmin, requireSuperAdmin, updateSecuritySettings);
router.put('/features', protectAdmin, requireSuperAdmin, updateFeatureFlags);

export default router;
