// controllers/dashboard/AdminController.js
import { Admin, UserActivity } from '../../models/dashboard/index.js';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';

/**
 * Helper function to generate admin JWT token
 */
const generateAdminToken = (admin) => {
  return jwt.sign(
    { 
      id: admin._id, 
      email: admin.email, 
      role: admin.role,
      type: 'admin' 
    },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRY_DURATION || '24h' }
  );
};

/**
 * Helper function to format admin response
 */
const formatAdminResponse = (admin) => {
  const adminObject = admin.toObject ? admin.toObject() : admin;
  return {
    id: adminObject._id.toString(),
    name: adminObject.name,
    email: adminObject.email,
    role: adminObject.role,
    permissions: adminObject.permissions,
    isActive: adminObject.isActive,
    lastLogin: adminObject.lastLogin,
    createdAt: adminObject.createdAt,
    updatedAt: adminObject.updatedAt
  };
};

/**
 * @desc    Admin login
 * @route   POST /api/dashboard/auth/login
 * @access  Public
 */
export const loginAdmin = async (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({ 
        success: false, 
        error: 'Please provide email and password.' 
      });
    }

    // Find admin by email
    const admin = await Admin.findOne({ email });
    if (!admin) {
      return res.status(401).json({ 
        success: false, 
        error: 'Invalid credentials.' 
      });
    }

    // Check if account is locked
    if (admin.isLocked) {
      return res.status(423).json({ 
        success: false, 
        error: 'Account is temporarily locked due to too many failed login attempts.' 
      });
    }

    // Check if account is active
    if (!admin.isActive) {
      return res.status(403).json({ 
        success: false, 
        error: 'Account is deactivated. Please contact a super admin.' 
      });
    }

    // Verify password
    const isMatch = await admin.comparePassword(password);
    if (!isMatch) {
      await admin.incLoginAttempts();
      return res.status(401).json({ 
        success: false, 
        error: 'Invalid credentials.' 
      });
    }

    // Reset login attempts and update last login
    await admin.resetLoginAttempts();

    // Generate token
    const token = generateAdminToken(admin);

    // Log the login activity
    await UserActivity.create({
      userId: null,
      activityType: 'admin_login',
      metadata: {
        adminId: admin._id,
        adminEmail: admin.email,
        adminRole: admin.role
      },
      success: true
    });

    res.json({
      success: true,
      message: 'Login successful.',
      data: {
        token,
        admin: formatAdminResponse(admin)
      }
    });

  } catch (error) {
    console.error('Error in loginAdmin:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Server error during login.' 
    });
  }
};

/**
 * @desc    Get current admin profile
 * @route   GET /api/dashboard/auth/me
 * @access  Private (Admin only)
 */
export const getCurrentAdmin = async (req, res) => {
  try {
    const admin = await Admin.findById(req.admin.id).select('-password');
    if (!admin) {
      return res.status(404).json({ 
        success: false, 
        error: 'Admin not found.' 
      });
    }

    res.json({
      success: true,
      data: { admin: formatAdminResponse(admin) }
    });

  } catch (error) {
    console.error('Error in getCurrentAdmin:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Server error while fetching admin profile.' 
    });
  }
};

/**
 * @desc    Register first super admin (only if no admins exist)
 * @route   POST /api/dashboard/admins/register
 * @access  Public (only works if no admins exist)
 */
export const registerFirstAdmin = async (req, res) => {
  try {
    // Check if any admin already exists
    const existingAdminCount = await Admin.countDocuments();
    if (existingAdminCount > 0) {
      return res.status(403).json({
        success: false,
        error: 'Registration is disabled. Admin users already exist.'
      });
    }

    const { name, email, password } = req.body;

    // Validate required fields
    if (!name || !email || !password) {
      return res.status(400).json({
        success: false,
        error: 'Name, email, and password are required.'
      });
    }

    // Create first super admin
    const admin = new Admin({
      name,
      email,
      password,
      role: 'super_admin',
      isActive: true,
      permissions: {
        userManagement: true,
        limitsManagement: true,
        systemSettings: true,
        analytics: true,
        adminManagement: true,
      }
    });

    await admin.save();

    // Generate token
    const token = generateAdminToken(admin);

    res.status(201).json({
      success: true,
      message: 'First super admin created successfully.',
      data: {
        token,
        admin: formatAdminResponse(admin)
      }
    });

  } catch (error) {
    console.error('Error in registerFirstAdmin:', error);
    if (error.code === 11000) {
      return res.status(409).json({
        success: false,
        error: 'Admin with this email already exists.'
      });
    }
    res.status(500).json({
      success: false,
      error: 'Server error while creating admin.'
    });
  }
};

/**
 * @desc    Check if registration is available
 * @route   GET /api/dashboard/admins/registration-status
 * @access  Public
 */
export const getRegistrationStatus = async (req, res) => {
  try {
    const adminCount = await Admin.countDocuments();
    res.json({
      success: true,
      data: {
        registrationAvailable: adminCount === 0,
        adminCount
      }
    });
  } catch (error) {
    console.error('Error in getRegistrationStatus:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while checking registration status.'
    });
  }
};

/**
 * @desc    Create new admin
 * @route   POST /api/dashboard/admins
 * @access  Private (Super Admin only)
 */
export const createAdmin = async (req, res) => {
  try {
    const { name, email, password, role = 'admin' } = req.body;

    // Validate required fields
    if (!name || !email || !password) {
      return res.status(400).json({ 
        success: false, 
        error: 'Name, email, and password are required.' 
      });
    }

    // Check if admin already exists
    const existingAdmin = await Admin.findOne({ email });
    if (existingAdmin) {
      return res.status(409).json({ 
        success: false, 
        error: 'Admin with this email already exists.' 
      });
    }

    // Create new admin
    const admin = new Admin({
      name,
      email,
      password,
      role,
      createdBy: req.admin.id
    });

    await admin.save();

    // Log the admin creation
    await UserActivity.create({
      userId: null,
      activityType: 'admin_creation',
      metadata: {
        createdBy: req.admin.id,
        newAdminId: admin._id,
        newAdminEmail: admin.email,
        newAdminRole: admin.role
      },
      success: true
    });

    res.status(201).json({
      success: true,
      message: 'Admin created successfully.',
      data: { admin: formatAdminResponse(admin) }
    });

  } catch (error) {
    console.error('Error in createAdmin:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Server error while creating admin.' 
    });
  }
};

/**
 * @desc    Get all admins
 * @route   GET /api/dashboard/admins
 * @access  Private (Super Admin only)
 */
export const getAllAdmins = async (req, res) => {
  try {
    const admins = await Admin.find()
      .select('-password')
      .sort({ createdAt: -1 })
      .lean();

    const formattedAdmins = admins.map(formatAdminResponse);

    res.json({
      success: true,
      data: { admins: formattedAdmins }
    });

  } catch (error) {
    console.error('Error in getAllAdmins:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Server error while fetching admins.' 
    });
  }
};

/**
 * @desc    Update admin
 * @route   PUT /api/dashboard/admins/:id
 * @access  Private (Super Admin only)
 */
export const updateAdmin = async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    // Remove sensitive fields that shouldn't be updated directly
    delete updates.password;
    delete updates._id;
    delete updates.__v;
    delete updates.createdBy;

    const admin = await Admin.findByIdAndUpdate(
      id,
      { $set: updates },
      { new: true, runValidators: true }
    ).select('-password');

    if (!admin) {
      return res.status(404).json({ 
        success: false, 
        error: 'Admin not found.' 
      });
    }

    // Log the admin update
    await UserActivity.create({
      userId: null,
      activityType: 'admin_update',
      metadata: {
        updatedBy: req.admin.id,
        updatedAdminId: admin._id,
        updatedFields: Object.keys(updates)
      },
      success: true
    });

    res.json({
      success: true,
      message: 'Admin updated successfully.',
      data: { admin: formatAdminResponse(admin) }
    });

  } catch (error) {
    console.error('Error in updateAdmin:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Server error while updating admin.' 
    });
  }
};

/**
 * @desc    Delete admin
 * @route   DELETE /api/dashboard/admins/:id
 * @access  Private (Super Admin only)
 */
export const deleteAdmin = async (req, res) => {
  try {
    const { id } = req.params;

    // Prevent self-deletion
    if (id === req.admin.id) {
      return res.status(400).json({ 
        success: false, 
        error: 'Cannot delete your own admin account.' 
      });
    }

    const admin = await Admin.findById(id);
    if (!admin) {
      return res.status(404).json({ 
        success: false, 
        error: 'Admin not found.' 
      });
    }

    // Store admin info for logging
    const adminInfo = {
      email: admin.email,
      name: admin.name,
      role: admin.role
    };

    await Admin.findByIdAndDelete(id);

    // Log the admin deletion
    await UserActivity.create({
      userId: null,
      activityType: 'admin_deletion',
      metadata: {
        deletedBy: req.admin.id,
        deletedAdmin: adminInfo
      },
      success: true
    });

    res.json({
      success: true,
      message: 'Admin deleted successfully.'
    });

  } catch (error) {
    console.error('Error in deleteAdmin:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Server error while deleting admin.' 
    });
  }
};

/**
 * @desc    Change admin password
 * @route   PUT /api/dashboard/auth/change-password
 * @access  Private (Admin only)
 */
export const changeAdminPassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    if (!currentPassword || !newPassword) {
      return res.status(400).json({ 
        success: false, 
        error: 'Current password and new password are required.' 
      });
    }

    if (newPassword.length < 8) {
      return res.status(400).json({ 
        success: false, 
        error: 'New password must be at least 8 characters long.' 
      });
    }

    const admin = await Admin.findById(req.admin.id);
    if (!admin) {
      return res.status(404).json({ 
        success: false, 
        error: 'Admin not found.' 
      });
    }

    // Verify current password
    const isMatch = await admin.comparePassword(currentPassword);
    if (!isMatch) {
      return res.status(401).json({ 
        success: false, 
        error: 'Current password is incorrect.' 
      });
    }

    // Update password
    admin.password = newPassword;
    await admin.save();

    // Log the password change
    await UserActivity.create({
      userId: null,
      activityType: 'admin_password_change',
      metadata: {
        adminId: admin._id,
        adminEmail: admin.email
      },
      success: true
    });

    res.json({
      success: true,
      message: 'Password changed successfully.'
    });

  } catch (error) {
    console.error('Error in changeAdminPassword:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Server error while changing password.' 
    });
  }
};
