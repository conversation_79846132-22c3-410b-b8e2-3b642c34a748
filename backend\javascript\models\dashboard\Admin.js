// models/dashboard/Admin.js
import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';

/**
 * Defines the schema for admin users with enhanced permissions and access controls
 */
const AdminSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
  },
  email: {
    type: String,
    required: true,
    unique: true,
    match: [/\S+@\S+\.\S+/, 'is invalid'],
    lowercase: true,
    trim: true,
  },
  password: {
    type: String,
    required: true,
    minlength: 8,
  },
  role: {
    type: String,
    enum: ['super_admin', 'admin', 'moderator'],
    default: 'admin',
  },
  permissions: {
    userManagement: {
      type: Boolean,
      default: true,
    },
    limitsManagement: {
      type: Boolean,
      default: true,
    },
    systemSettings: {
      type: Boolean,
      default: false, // Only super_admin by default
    },
    analytics: {
      type: Boolean,
      default: true,
    },
    adminManagement: {
      type: Boolean,
      default: false, // Only super_admin by default
    },
  },
  isActive: {
    type: <PERSON>olean,
    default: true,
  },
  lastLogin: {
    type: Date,
    default: null,
  },
  loginAttempts: {
    type: Number,
    default: 0,
  },
  lockUntil: {
    type: Date,
    default: null,
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: false,
  },
}, { timestamps: true });

// Virtual for checking if account is locked
AdminSchema.virtual('isLocked').get(function() {
  return !!(this.lockUntil && this.lockUntil > Date.now());
});

// Middleware to hash the password before saving
AdminSchema.pre('save', async function (next) {
  if (!this.isModified('password')) {
    return next();
  }
  try {
    const salt = await bcrypt.genSalt(12); // Higher salt rounds for admin accounts
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (err) {
    next(err);
  }
});

// Method to compare password
AdminSchema.methods.comparePassword = async function (candidatePassword) {
  try {
    return await bcrypt.compare(candidatePassword, this.password);
  } catch (error) {
    console.error("Error comparing admin password:", error);
    return false;
  }
};

// Method to increment login attempts
AdminSchema.methods.incLoginAttempts = function() {
  // If we have a previous lock that has expired, restart at 1
  if (this.lockUntil && this.lockUntil < Date.now()) {
    return this.updateOne({
      $unset: { lockUntil: 1 },
      $set: { loginAttempts: 1 }
    });
  }
  
  const updates = { $inc: { loginAttempts: 1 } };
  
  // Lock account after 5 failed attempts for 2 hours
  if (this.loginAttempts + 1 >= 5 && !this.isLocked) {
    updates.$set = { lockUntil: Date.now() + 2 * 60 * 60 * 1000 }; // 2 hours
  }
  
  return this.updateOne(updates);
};

// Method to reset login attempts
AdminSchema.methods.resetLoginAttempts = function() {
  return this.updateOne({
    $unset: { loginAttempts: 1, lockUntil: 1 },
    $set: { lastLogin: new Date() }
  });
};

// Method to check if admin has specific permission
AdminSchema.methods.hasPermission = function(permission) {
  if (this.role === 'super_admin') return true;
  return this.permissions[permission] || false;
};

// Set permissions based on role before saving
AdminSchema.pre('save', function(next) {
  if (this.role === 'super_admin') {
    this.permissions = {
      userManagement: true,
      limitsManagement: true,
      systemSettings: true,
      analytics: true,
      adminManagement: true,
    };
  } else if (this.role === 'moderator') {
    this.permissions = {
      userManagement: true,
      limitsManagement: false,
      systemSettings: false,
      analytics: true,
      adminManagement: false,
    };
  }
  next();
});

const Admin = mongoose.model('Admin', AdminSchema);

export default Admin;
