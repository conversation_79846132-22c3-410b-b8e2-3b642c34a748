// routes/dashboard/userManagementRoutes.js
import express from 'express';
import {
  getAllUsers,
  getUserDetails,
  updateUser,
  deleteUser,
  updateUserLimits,
  bulkUpdateUsers
} from '../../controllers/dashboard/index.js';
import { protectAdmin, requirePermission } from '../../middleware/adminAuthMiddleware.js';

const router = express.Router();

// User management routes
router.get('/', protectAdmin, requirePermission('userManagement'), getAllUsers);
router.get('/:id', protectAdmin, requirePermission('userManagement'), getUserDetails);
router.put('/:id', protectAdmin, requirePermission('userManagement'), updateUser);
router.delete('/:id', protectAdmin, requirePermission('userManagement'), deleteUser);
router.put('/:id/limits', protectAdmin, requirePermission('limitsManagement'), updateUserLimits);
router.put('/bulk-update', protectAdmin, requirePermission('userManagement'), bulkUpdateUsers);

export default router;
