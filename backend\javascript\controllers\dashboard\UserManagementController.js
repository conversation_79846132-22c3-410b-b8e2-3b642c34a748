// controllers/dashboard/UserManagementController.js
import User from '../../models/User.js';
import { UserActivity } from '../../models/dashboard/index.js';

/**
 * @desc    Get all users with pagination and filtering
 * @route   GET /api/dashboard/users
 * @access  Private (Admin only)
 */
export const getAllUsers = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search = '',
      planFilter = '',
      statusFilter = '',
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    // Build filter query
    const filter = {};
    
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }

    if (planFilter) {
      filter['subscription.planName'] = planFilter;
    }

    if (statusFilter) {
      if (statusFilter === 'verified') {
        filter.isVerified = true;
      } else if (statusFilter === 'unverified') {
        filter.isVerified = false;
      } else if (statusFilter === 'active') {
        filter['subscription.status'] = 'active';
      } else if (statusFilter === 'inactive') {
        filter['subscription.status'] = { $ne: 'active' };
      }
    }

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Get users with pagination
    const users = await User.find(filter)
      .select('-password')
      .sort(sort)
      .skip(skip)
      .limit(limitNum)
      .lean();

    // Get total count for pagination
    const totalUsers = await User.countDocuments(filter);
    const totalPages = Math.ceil(totalUsers / limitNum);

    // Enhance user data with recent activity
    const enhancedUsers = await Promise.all(
      users.map(async (user) => {
        const recentActivity = await UserActivity.findOne({
          userId: user._id
        })
        .sort({ createdAt: -1 })
        .select('activityType createdAt')
        .lean();

        const activityCount = await UserActivity.countDocuments({
          userId: user._id,
          createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } // Last 30 days
        });

        return {
          ...user,
          lastActivity: recentActivity,
          activityCount30Days: activityCount
        };
      })
    );

    res.json({
      success: true,
      data: {
        users: enhancedUsers,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalUsers,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1
        }
      }
    });

  } catch (error) {
    console.error('Error in getAllUsers:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Server error while fetching users.' 
    });
  }
};

/**
 * @desc    Get single user details
 * @route   GET /api/dashboard/users/:id
 * @access  Private (Admin only)
 */
export const getUserDetails = async (req, res) => {
  try {
    const { id } = req.params;

    const user = await User.findById(id).select('-password').lean();
    if (!user) {
      return res.status(404).json({ 
        success: false, 
        error: 'User not found.' 
      });
    }

    // Get user's activity history
    const activityHistory = await UserActivity.find({ userId: id })
      .sort({ createdAt: -1 })
      .limit(50)
      .lean();

    // Get user's activity statistics
    const activityStats = await UserActivity.aggregate([
      { $match: { userId: user._id } },
      {
        $group: {
          _id: '$activityType',
          count: { $sum: 1 },
          lastActivity: { $max: '$createdAt' }
        }
      },
      { $sort: { count: -1 } }
    ]);

    res.json({
      success: true,
      data: {
        user,
        activityHistory,
        activityStats
      }
    });

  } catch (error) {
    console.error('Error in getUserDetails:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Server error while fetching user details.' 
    });
  }
};

/**
 * @desc    Update user details
 * @route   PUT /api/dashboard/users/:id
 * @access  Private (Admin only)
 */
export const updateUser = async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    // Remove sensitive fields that shouldn't be updated directly
    delete updates.password;
    delete updates._id;
    delete updates.__v;

    const user = await User.findByIdAndUpdate(
      id,
      { $set: updates },
      { new: true, runValidators: true }
    ).select('-password');

    if (!user) {
      return res.status(404).json({ 
        success: false, 
        error: 'User not found.' 
      });
    }

    // Log the admin action
    await UserActivity.create({
      userId: user._id,
      activityType: 'profile_update',
      metadata: {
        updatedBy: req.admin.id,
        updatedFields: Object.keys(updates)
      },
      success: true
    });

    res.json({
      success: true,
      message: 'User updated successfully.',
      data: { user }
    });

  } catch (error) {
    console.error('Error in updateUser:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Server error while updating user.' 
    });
  }
};

/**
 * @desc    Delete user
 * @route   DELETE /api/dashboard/users/:id
 * @access  Private (Admin only)
 */
export const deleteUser = async (req, res) => {
  try {
    const { id } = req.params;

    const user = await User.findById(id);
    if (!user) {
      return res.status(404).json({ 
        success: false, 
        error: 'User not found.' 
      });
    }

    // Store user info for logging before deletion
    const userInfo = {
      email: user.email,
      name: user.name,
      planName: user.subscription?.planName
    };

    // Delete user and their activity records
    await Promise.all([
      User.findByIdAndDelete(id),
      UserActivity.deleteMany({ userId: id })
    ]);

    // Log the admin action
    await UserActivity.create({
      userId: null, // User is deleted, so no userId
      activityType: 'user_deletion',
      metadata: {
        deletedBy: req.admin.id,
        deletedUser: userInfo
      },
      success: true
    });

    res.json({
      success: true,
      message: 'User deleted successfully.'
    });

  } catch (error) {
    console.error('Error in deleteUser:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Server error while deleting user.' 
    });
  }
};

/**
 * @desc    Update user subscription limits
 * @route   PUT /api/dashboard/users/:id/limits
 * @access  Private (Admin only)
 */
export const updateUserLimits = async (req, res) => {
  try {
    const { id } = req.params;
    const { limits } = req.body;

    const user = await User.findById(id);
    if (!user) {
      return res.status(404).json({ 
        success: false, 
        error: 'User not found.' 
      });
    }

    // Update subscription limits
    if (limits.planName) {
      user.subscription.planName = limits.planName;
    }

    // Update usage counts if provided
    const allowedFields = [
      'freeTierUploadCount',
      'freeTierBusinessPlanCount',
      'freeTierInvestorPitchCount',
      'freeTierBusinessQACount',
      'freeTierMessageCount',
      'proTierUploadCount',
      'proTierBusinessPlanCount',
      'proTierInvestorPitchCount',
      'proTierBusinessQACount',
      'proTierMessageCount'
    ];

    allowedFields.forEach(field => {
      if (limits[field] !== undefined) {
        user.subscription[field] = limits[field];
      }
    });

    await user.save();

    // Log the admin action
    await UserActivity.create({
      userId: user._id,
      activityType: 'limit_update',
      metadata: {
        updatedBy: req.admin.id,
        newLimits: limits
      },
      success: true
    });

    res.json({
      success: true,
      message: 'User limits updated successfully.',
      data: { 
        subscription: user.subscription 
      }
    });

  } catch (error) {
    console.error('Error in updateUserLimits:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Server error while updating user limits.' 
    });
  }
};

/**
 * @desc    Bulk update users
 * @route   PUT /api/dashboard/users/bulk-update
 * @access  Private (Admin only)
 */
export const bulkUpdateUsers = async (req, res) => {
  try {
    const { userIds, updates } = req.body;

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return res.status(400).json({ 
        success: false, 
        error: 'User IDs array is required.' 
      });
    }

    // Remove sensitive fields
    delete updates.password;
    delete updates._id;
    delete updates.__v;

    const result = await User.updateMany(
      { _id: { $in: userIds } },
      { $set: updates },
      { runValidators: true }
    );

    // Log the admin action
    await UserActivity.create({
      userId: null,
      activityType: 'bulk_user_update',
      metadata: {
        updatedBy: req.admin.id,
        affectedUsers: userIds.length,
        updates: Object.keys(updates)
      },
      success: true
    });

    res.json({
      success: true,
      message: `${result.modifiedCount} users updated successfully.`,
      data: {
        matchedCount: result.matchedCount,
        modifiedCount: result.modifiedCount
      }
    });

  } catch (error) {
    console.error('Error in bulkUpdateUsers:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Server error while bulk updating users.' 
    });
  }
};
