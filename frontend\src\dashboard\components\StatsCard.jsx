// src/dashboard/components/StatsCard.jsx
import React from 'react';
import { FiTrendingUp, FiTrendingDown } from 'react-icons/fi';

const StatsCard = ({
  title,
  value,
  change,
  changeType = 'neutral', // 'positive', 'negative', 'neutral'
  icon: Icon,
  iconColor = 'text-blue-600',
  iconBgColor = 'bg-blue-100',
  loading = false,
  subtitle,
  onClick
}) => {
  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="flex items-center justify-between">
            <div>
              <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-16"></div>
            </div>
            <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
          </div>
        </div>
      </div>
    );
  }

  const formatValue = (val) => {
    if (typeof val === 'number') {
      if (val >= 1000000) {
        return `${(val / 1000000).toFixed(1)}M`;
      } else if (val >= 1000) {
        return `${(val / 1000).toFixed(1)}K`;
      }
      return val.toLocaleString();
    }
    return val;
  };

  const getChangeColor = () => {
    switch (changeType) {
      case 'positive':
        return 'text-green-600';
      case 'negative':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getTrendIcon = () => {
    if (changeType === 'positive') {
      return <FiTrendingUp className="w-4 h-4" />;
    } else if (changeType === 'negative') {
      return <FiTrendingDown className="w-4 h-4" />;
    }
    return null;
  };

  return (
    <div
      className={`bg-gradient-to-br from-white to-gray-50 rounded-xl shadow-sm border border-gray-100 p-6 transition-all duration-300 ${
        onClick ? 'cursor-pointer hover:shadow-lg hover:scale-[1.02] hover:border-blue-200' : 'hover:shadow-md'
      }`}
      onClick={onClick}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-3">
            {Icon && (
              <div className={`p-2 rounded-lg ${iconBgColor} ring-1 ring-white/20`}>
                <Icon className={`w-5 h-5 ${iconColor}`} />
              </div>
            )}
            <p className="text-sm font-semibold text-gray-700 uppercase tracking-wide">{title}</p>
          </div>

          <div className="mb-3">
            <p className="text-4xl font-bold text-gray-900 leading-none">
              {formatValue(value)}
            </p>
            {subtitle && (
              <p className="text-sm text-gray-600 mt-1">{subtitle}</p>
            )}
          </div>

          {change !== undefined && (
            <div className={`inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium ${
              changeType === 'positive'
                ? 'bg-green-50 text-green-700 border border-green-200'
                : changeType === 'negative'
                ? 'bg-red-50 text-red-700 border border-red-200'
                : 'bg-gray-50 text-gray-700 border border-gray-200'
            }`}>
              {getTrendIcon()}
              <span>
                {changeType === 'positive' ? '+' : ''}
                {change}
                {typeof change === 'number' ? '%' : ''}
              </span>
              <span className="text-gray-500">vs last period</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default StatsCard;
