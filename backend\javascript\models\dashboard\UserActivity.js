// models/dashboard/UserActivity.js
import mongoose from 'mongoose';

/**
 * Defines the schema for tracking user activity and system usage analytics
 */
const UserActivitySchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true,
  },
  
  // Activity details
  activityType: {
    type: String,
    enum: [
      'login',
      'logout',
      'registration',
      'email_verification',
      'password_change',
      'profile_update',
      'subscription_change',
      'file_upload',
      'file_download',
      'ai_chat_message',
      'business_plan_generation',
      'investor_pitch_generation',
      'business_qa_query',
      'pdf_analysis',
      'payment_initiated',
      'payment_completed',
      'payment_failed',
      'limit_exceeded',
      'feature_access',
      'error_occurred',
    ],
    required: true,
    index: true,
  },
  
  // Activity metadata
  metadata: {
    // For file operations
    fileName: String,
    fileSize: Number,
    fileType: String,
    
    // For AI operations
    promptLength: Number,
    responseLength: Number,
    processingTime: Number, // milliseconds
    
    // For payments
    amount: Number,
    currency: String,
    paymentMethod: String,
    transactionId: String,
    
    // For errors
    errorCode: String,
    errorMessage: String,
    
    // For feature access
    featureName: String,
    planRequired: String,
    
    // General metadata
    userAgent: String,
    ipAddress: String,
    sessionId: String,
    referrer: String,
  },
  
  // Request details
  requestInfo: {
    method: String,
    endpoint: String,
    statusCode: Number,
    responseTime: Number, // milliseconds
    requestSize: Number, // bytes
    responseSize: Number, // bytes
  },
  
  // Geolocation (if available)
  location: {
    country: String,
    region: String,
    city: String,
    timezone: String,
    coordinates: {
      latitude: Number,
      longitude: Number,
    },
  },
  
  // Device information
  deviceInfo: {
    platform: String, // web, mobile, desktop
    browser: String,
    browserVersion: String,
    os: String,
    osVersion: String,
    deviceType: String, // mobile, tablet, desktop
    screenResolution: String,
  },
  
  // Success/failure status
  success: {
    type: Boolean,
    default: true,
  },
  
  // Additional context
  context: {
    previousActivity: String,
    sessionDuration: Number, // seconds
    pageViews: Number,
    isNewUser: Boolean,
    subscriptionPlan: String,
  },
  
}, { timestamps: true });

// Indexes for efficient querying
UserActivitySchema.index({ userId: 1, createdAt: -1 });
UserActivitySchema.index({ activityType: 1, createdAt: -1 });
UserActivitySchema.index({ 'metadata.featureName': 1, createdAt: -1 });
UserActivitySchema.index({ success: 1, createdAt: -1 });
UserActivitySchema.index({ createdAt: -1 }); // For time-based queries

// TTL index to automatically delete old records (90 days)
UserActivitySchema.index({ createdAt: 1 }, { expireAfterSeconds: 90 * 24 * 60 * 60 });

// Static methods for analytics
UserActivitySchema.statics.getActivityStats = async function(startDate, endDate, filters = {}) {
  const matchStage = {
    createdAt: { $gte: startDate, $lte: endDate },
    ...filters,
  };
  
  return await this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: '$activityType',
        count: { $sum: 1 },
        successCount: { $sum: { $cond: ['$success', 1, 0] } },
        failureCount: { $sum: { $cond: ['$success', 0, 1] } },
        avgResponseTime: { $avg: '$requestInfo.responseTime' },
      },
    },
    { $sort: { count: -1 } },
  ]);
};

UserActivitySchema.statics.getUserEngagement = async function(startDate, endDate) {
  return await this.aggregate([
    { $match: { createdAt: { $gte: startDate, $lte: endDate } } },
    {
      $group: {
        _id: '$userId',
        totalActivities: { $sum: 1 },
        uniqueFeatures: { $addToSet: '$metadata.featureName' },
        lastActivity: { $max: '$createdAt' },
        avgSessionDuration: { $avg: '$context.sessionDuration' },
      },
    },
    {
      $project: {
        totalActivities: 1,
        uniqueFeaturesCount: { $size: '$uniqueFeatures' },
        lastActivity: 1,
        avgSessionDuration: 1,
        engagementScore: {
          $multiply: [
            { $log: { $add: ['$totalActivities', 1] } },
            { $size: '$uniqueFeatures' },
          ],
        },
      },
    },
    { $sort: { engagementScore: -1 } },
  ]);
};

UserActivitySchema.statics.getErrorAnalysis = async function(startDate, endDate) {
  return await this.aggregate([
    {
      $match: {
        createdAt: { $gte: startDate, $lte: endDate },
        success: false,
      },
    },
    {
      $group: {
        _id: {
          errorCode: '$metadata.errorCode',
          endpoint: '$requestInfo.endpoint',
        },
        count: { $sum: 1 },
        users: { $addToSet: '$userId' },
        avgResponseTime: { $avg: '$requestInfo.responseTime' },
        lastOccurrence: { $max: '$createdAt' },
      },
    },
    {
      $project: {
        errorCode: '$_id.errorCode',
        endpoint: '$_id.endpoint',
        count: 1,
        affectedUsers: { $size: '$users' },
        avgResponseTime: 1,
        lastOccurrence: 1,
      },
    },
    { $sort: { count: -1 } },
  ]);
};

// Instance methods
UserActivitySchema.methods.isRecentActivity = function(minutes = 30) {
  const cutoff = new Date(Date.now() - minutes * 60 * 1000);
  return this.createdAt > cutoff;
};

UserActivitySchema.methods.getDuration = function() {
  return this.context?.sessionDuration || 0;
};

const UserActivity = mongoose.model('UserActivity', UserActivitySchema);

export default UserActivity;
