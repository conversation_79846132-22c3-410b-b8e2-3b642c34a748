// scripts/createSuperAdmin.js
import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { Admin } from '../models/dashboard/index.js';

// Load environment variables
dotenv.config();

const createSuperAdmin = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log('Connected to MongoDB');

    // Check if super admin already exists
    const existingSuperAdmin = await Admin.findOne({ role: 'super_admin' });
    
    if (existingSuperAdmin) {
      console.log('Super admin already exists:', existingSuperAdmin.email);
      process.exit(0);
    }

    // Get admin details from command line arguments or use defaults
    const email = process.argv[2] || '<EMAIL>';
    const password = process.argv[3] || 'SuperAdmin123!';
    const name = process.argv[4] || 'Super Administrator';

    // Check if admin with this email already exists
    const existingAdmin = await Admin.findOne({ email });
    if (existingAdmin) {
      console.log('Admin with this email already exists:', email);
      process.exit(1);
    }

    // Create super admin
    const superAdmin = new Admin({
      name,
      email,
      password,
      role: 'super_admin',
      isActive: true,
      permissions: {
        userManagement: true,
        limitsManagement: true,
        systemSettings: true,
        analytics: true,
        adminManagement: true,
      }
    });

    await superAdmin.save();

    console.log('Super admin created successfully!');
    console.log('Email:', email);
    console.log('Password:', password);
    console.log('Role:', 'super_admin');
    console.log('\nPlease change the password after first login.');

  } catch (error) {
    console.error('Error creating super admin:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
};

// Run the script
createSuperAdmin();
