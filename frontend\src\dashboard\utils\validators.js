// src/dashboard/utils/validators.js

/**
 * Email validation
 */
export const validateEmail = (email) => {
  if (!email) return { isValid: false, error: 'Email is required' };
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return { isValid: false, error: 'Please enter a valid email address' };
  }
  
  return { isValid: true };
};

/**
 * Password validation
 */
export const validatePassword = (password, minLength = 8) => {
  if (!password) return { isValid: false, error: 'Password is required' };
  
  if (password.length < minLength) {
    return { isValid: false, error: `Password must be at least ${minLength} characters long` };
  }
  
  // Check for at least one uppercase letter
  if (!/[A-Z]/.test(password)) {
    return { isValid: false, error: 'Password must contain at least one uppercase letter' };
  }
  
  // Check for at least one lowercase letter
  if (!/[a-z]/.test(password)) {
    return { isValid: false, error: 'Password must contain at least one lowercase letter' };
  }
  
  // Check for at least one number
  if (!/\d/.test(password)) {
    return { isValid: false, error: 'Password must contain at least one number' };
  }
  
  return { isValid: true };
};

/**
 * Name validation
 */
export const validateName = (name) => {
  if (!name) return { isValid: false, error: 'Name is required' };
  
  if (name.trim().length < 2) {
    return { isValid: false, error: 'Name must be at least 2 characters long' };
  }
  
  if (name.trim().length > 50) {
    return { isValid: false, error: 'Name must be less than 50 characters' };
  }
  
  // Check for valid characters (letters, spaces, hyphens, apostrophes)
  if (!/^[a-zA-Z\s\-']+$/.test(name)) {
    return { isValid: false, error: 'Name can only contain letters, spaces, hyphens, and apostrophes' };
  }
  
  return { isValid: true };
};

/**
 * Phone number validation
 */
export const validatePhoneNumber = (phone) => {
  if (!phone) return { isValid: false, error: 'Phone number is required' };
  
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '');
  
  if (cleaned.length < 10) {
    return { isValid: false, error: 'Phone number must be at least 10 digits' };
  }
  
  if (cleaned.length > 15) {
    return { isValid: false, error: 'Phone number must be less than 15 digits' };
  }
  
  return { isValid: true };
};

/**
 * URL validation
 */
export const validateURL = (url) => {
  if (!url) return { isValid: false, error: 'URL is required' };
  
  try {
    new URL(url);
    return { isValid: true };
  } catch {
    return { isValid: false, error: 'Please enter a valid URL' };
  }
};

/**
 * Number validation
 */
export const validateNumber = (value, min = null, max = null) => {
  if (value === '' || value === null || value === undefined) {
    return { isValid: false, error: 'Number is required' };
  }
  
  const num = Number(value);
  
  if (isNaN(num)) {
    return { isValid: false, error: 'Please enter a valid number' };
  }
  
  if (min !== null && num < min) {
    return { isValid: false, error: `Number must be at least ${min}` };
  }
  
  if (max !== null && num > max) {
    return { isValid: false, error: `Number must be at most ${max}` };
  }
  
  return { isValid: true };
};

/**
 * Required field validation
 */
export const validateRequired = (value, fieldName = 'Field') => {
  if (!value || (typeof value === 'string' && value.trim() === '')) {
    return { isValid: false, error: `${fieldName} is required` };
  }
  
  return { isValid: true };
};

/**
 * Admin role validation
 */
export const validateAdminRole = (role) => {
  const validRoles = ['super_admin', 'admin', 'moderator'];
  
  if (!role) return { isValid: false, error: 'Role is required' };
  
  if (!validRoles.includes(role)) {
    return { isValid: false, error: 'Please select a valid role' };
  }
  
  return { isValid: true };
};

/**
 * User plan validation
 */
export const validateUserPlan = (plan) => {
  const validPlans = ['Starter', 'Pro', 'Enterprise'];
  
  if (!plan) return { isValid: false, error: 'Plan is required' };
  
  if (!validPlans.includes(plan)) {
    return { isValid: false, error: 'Please select a valid plan' };
  }
  
  return { isValid: true };
};

/**
 * Limit value validation
 */
export const validateLimit = (value, fieldName = 'Limit') => {
  if (value === '' || value === null || value === undefined) {
    return { isValid: false, error: `${fieldName} is required` };
  }
  
  const num = Number(value);
  
  if (isNaN(num)) {
    return { isValid: false, error: `${fieldName} must be a valid number` };
  }
  
  if (num < -1) {
    return { isValid: false, error: `${fieldName} must be -1 (unlimited) or a positive number` };
  }
  
  if (num === 0) {
    return { isValid: false, error: `${fieldName} cannot be 0. Use -1 for unlimited or a positive number` };
  }
  
  return { isValid: true };
};

/**
 * File size validation
 */
export const validateFileSize = (file, maxSizeMB = 10) => {
  if (!file) return { isValid: false, error: 'File is required' };
  
  const maxSizeBytes = maxSizeMB * 1024 * 1024;
  
  if (file.size > maxSizeBytes) {
    return { isValid: false, error: `File size must be less than ${maxSizeMB}MB` };
  }
  
  return { isValid: true };
};

/**
 * File type validation
 */
export const validateFileType = (file, allowedTypes = []) => {
  if (!file) return { isValid: false, error: 'File is required' };
  
  if (allowedTypes.length === 0) return { isValid: true };
  
  const fileExtension = file.name.split('.').pop().toLowerCase();
  
  if (!allowedTypes.includes(fileExtension)) {
    return { 
      isValid: false, 
      error: `File type must be one of: ${allowedTypes.join(', ')}` 
    };
  }
  
  return { isValid: true };
};

/**
 * Date validation
 */
export const validateDate = (date, fieldName = 'Date') => {
  if (!date) return { isValid: false, error: `${fieldName} is required` };
  
  const dateObj = new Date(date);
  
  if (isNaN(dateObj.getTime())) {
    return { isValid: false, error: `Please enter a valid ${fieldName.toLowerCase()}` };
  }
  
  return { isValid: true };
};

/**
 * Date range validation
 */
export const validateDateRange = (startDate, endDate) => {
  const startValidation = validateDate(startDate, 'Start date');
  if (!startValidation.isValid) return startValidation;
  
  const endValidation = validateDate(endDate, 'End date');
  if (!endValidation.isValid) return endValidation;
  
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  if (start >= end) {
    return { isValid: false, error: 'End date must be after start date' };
  }
  
  return { isValid: true };
};

/**
 * Validate form data
 */
export const validateForm = (data, validationRules) => {
  const errors = {};
  let isValid = true;
  
  Object.keys(validationRules).forEach(field => {
    const rules = validationRules[field];
    const value = data[field];
    
    for (const rule of rules) {
      const result = rule(value);
      if (!result.isValid) {
        errors[field] = result.error;
        isValid = false;
        break; // Stop at first error for this field
      }
    }
  });
  
  return { isValid, errors };
};

/**
 * Validate user data for creation/update
 */
export const validateUserData = (userData) => {
  const rules = {
    name: [validateName],
    email: [validateEmail]
  };
  
  if (userData.password) {
    rules.password = [validatePassword];
  }
  
  if (userData.phone) {
    rules.phone = [validatePhoneNumber];
  }
  
  return validateForm(userData, rules);
};

/**
 * Validate admin data for creation/update
 */
export const validateAdminData = (adminData) => {
  const rules = {
    name: [validateName],
    email: [validateEmail],
    role: [validateAdminRole]
  };
  
  if (adminData.password) {
    rules.password = [validatePassword];
  }
  
  return validateForm(adminData, rules);
};

/**
 * Validate system settings
 */
export const validateSystemSettings = (settings) => {
  const errors = {};
  let isValid = true;
  
  // Validate default limits
  if (settings.defaultLimits) {
    Object.keys(settings.defaultLimits).forEach(plan => {
      const limits = settings.defaultLimits[plan];
      Object.keys(limits).forEach(limitType => {
        const result = validateLimit(limits[limitType], `${plan} ${limitType}`);
        if (!result.isValid) {
          errors[`defaultLimits.${plan}.${limitType}`] = result.error;
          isValid = false;
        }
      });
    });
  }
  
  // Validate system config
  if (settings.systemConfig) {
    const config = settings.systemConfig;
    
    if (config.maxFileSize !== undefined) {
      const result = validateNumber(config.maxFileSize, 1, 100);
      if (!result.isValid) {
        errors['systemConfig.maxFileSize'] = result.error;
        isValid = false;
      }
    }
    
    if (config.passwordMinLength !== undefined) {
      const result = validateNumber(config.passwordMinLength, 6, 20);
      if (!result.isValid) {
        errors['systemConfig.passwordMinLength'] = result.error;
        isValid = false;
      }
    }
  }
  
  return { isValid, errors };
};
