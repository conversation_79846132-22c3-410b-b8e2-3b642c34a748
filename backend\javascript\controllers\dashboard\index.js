// controllers/dashboard/index.js

// Dashboard overview and analytics
export {
  getDashboardOverview,
  getSystemAnalytics,
  getPerformanceMetrics
} from './DashboardController.js';

// User management
export {
  getAllUsers,
  getUserDetails,
  updateUser,
  deleteUser,
  updateUserLimits,
  bulkUpdateUsers
} from './UserManagementController.js';

// Admin management
export {
  loginAdmin,
  getCurrentAdmin,
  createAdmin,
  getAllAdmins,
  updateAdmin,
  deleteAdmin,
  changeAdminPassword
} from './AdminController.js';

// System settings
export {
  getSystemSettings,
  updateSystemSettings,
  updateDefaultLimits,
  updateSystemConfig,
  updateRateLimiting,
  updateSecuritySettings,
  updateFeatureFlags,
  resetToDefaults,
  getSettingsHistory
} from './SystemSettingsController.js';
