// src/App.jsx
import React, { useState, useEffect, useCallback, Suspense } from "react";
import "./index.css";
import {
  BrowserRouter,
  Routes,
  Route,
  useLocation,
  Navigate,
  useNavigate,
} from "react-router-dom";
import { AnimatePresence } from "framer-motion";

// NavigationBar Import
import NavigationBar from "./common/Navbar/NavigationBar";

// Auth Imports
import AuthProvider, { useAuth } from "./context/AuthContext";
import { ToastProvider } from "./components/Tools/Pdf/mindmap/context/ToastContext";
import ProtectedRoute from "./components/common/Auth/ProtectedRoute";

// Common UI Imports
import LayoutManager from "./common/Layout/LayoutManager";
import AnimatedPageWrapper from "./common/Animations/AnimatedPageWrapper";
import Modal from "./common/modal/Modal";

// Application specific route handlers
import PdfRoutes from "./router/pdf/PdfRoutes";
const BusinessRoutes = React.lazy(() =>
  import(
    "./router/Business/BusinessRoutes"
  )
);
const AICharactersRoutes = React.lazy(() =>
  import(
    "./router/AICharecters/AICharactersRoutes"
  )
);

// --- LAZY LOADED PAGE COMPONENTS ---
const HomePage = React.lazy(() => import("./pages/common/Home/HomePage"));
const MainPage = React.lazy(() => import("./pages/common/Tools"));
const MindMapDisplayPage = React.lazy(() =>
  import("./pages/Tools/Pdf/mindmap/MindMapDisplayPage")
);
const PricingPage = React.lazy(() =>
  import("./pages/common/price/PricingPage")
);
const PaymentSuccessPage = React.lazy(() =>
  import("./pages/common/price/PaymentSuccessPage")
);
const PaymentCancelPage = React.lazy(() =>
  import("./pages/common/price/PaymentCancelPage")
);
const SettingsPlaceholder = React.lazy(() =>
  Promise.resolve({
    default: () => <div className="text-white p-6">Settings Page Content</div>,
  })
);
const HelpPlaceholder = React.lazy(() =>
  Promise.resolve({
    default: () => <div className="text-white p-6">Help Page Content</div>,
  })
);

// --- LAZY LOADED MODAL CONTENT ---
const LoginPageContent = React.lazy(() =>
  import("./pages/common/Auth/LoginPage")
);
const RegisterPageContent = React.lazy(() =>
  import("./pages/common/Auth/RegisterPage")
);
const VerificationModalContent = React.lazy(() =>
  import("./pages/common/Auth/VerificationPage")
);
const AuthPromptModalContent = React.lazy(() =>
  import("./common/modal/AuthPromptModal")
);
const ComingSoonModal = React.lazy(() =>
  import("./common/modal/ComingSoonModal")
);

// Dashboard Import
const Dashboard = React.lazy(() => import("./dashboard/Dashboard"));

const generalAppRoutesConfig = [
  {
    path: "/settings",
    LazyPageElement: SettingsPlaceholder,
    isProtected: false,
    showSidebar: true,
  },
  {
    path: "/help",
    LazyPageElement: HelpPlaceholder,
    isProtected: false,
    showSidebar: true,
  },
];

const GlobalSpinner = () => (
  <div className="fixed inset-0 flex items-center justify-center bg-slate-900 z-[100]">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-sky-500"></div>
  </div>
);

function App() {
  const location = useLocation();
  const navigate = useNavigate();
  const {
    isAuthenticated,
    isLoading: authLoadingGlobal,
    setVerificationEmail,
  } = useAuth();

  const [activeModal, setActiveModal] = useState(null);
  const [isComingSoonModalOpen, setIsComingSoonModalOpen] = useState(false);
  const [currentComingSoonToolName, setCurrentComingSoonToolName] =
    useState("");
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const openLoginModal = useCallback(() => {
    setActiveModal("login");
    setIsMenuOpen(false);
  }, []);
  const closeModal = useCallback(() => setActiveModal(null), []);
  const switchToRegister = useCallback(() => {
    setActiveModal("register");
    setIsMenuOpen(false);
  }, []);
  const switchToLogin = useCallback(() => {
    setActiveModal("login");
    setIsMenuOpen(false);
  }, []);
  const openAuthPromptModal = useCallback(() => {
    if (!isAuthenticated) {
      setActiveModal("authPrompt");
      setIsMenuOpen(false);
    }
  }, [isAuthenticated]);

  const handleRegisterSuccess = useCallback(
    (email) => {
      if (setVerificationEmail && email) setVerificationEmail(email);
      closeModal();
      setActiveModal("verify");
    },
    [closeModal, setVerificationEmail]
  );

  const handleVerificationSuccess = useCallback(
    () => closeModal(),
    [closeModal]
  );
  const handleBackToRegisterFromAuthModals = useCallback(
    () => setActiveModal("register"),
    []
  );

  const openComingSoonModal = useCallback((toolName) => {
    setCurrentComingSoonToolName(toolName);
    setIsComingSoonModalOpen(true);
    setIsMenuOpen(false);
  }, []);
  const closeComingSoonModal = useCallback(
    () => setIsComingSoonModalOpen(false),
    []
  );

  const handleGetStartedClick = useCallback(() => {
    setIsMenuOpen(false);
    if (!isAuthenticated) {
      openLoginModal();
    } else {
      navigate("/main");
    }
  }, [isAuthenticated, openLoginModal, navigate]);

  useEffect(() => {
    if (
      isAuthenticated &&
      (activeModal === "login" ||
        activeModal === "register" ||
        activeModal === "authPrompt" ||
        activeModal === "verify")
    ) {
      closeModal();
    }
  }, [isAuthenticated, activeModal, closeModal]);

  if (
    authLoadingGlobal &&
    !location.pathname.startsWith("/verify-email") &&
    !["/", "/pricing", "/main", "/payment/success", "/payment/cancel"].includes(
      location.pathname
    )
  ) {
    return <GlobalSpinner />;
  }

  const navBarVisibleRoutes = ["/", "/pricing", "/main"];
  const isMindMapDisplayPage = location.pathname.startsWith('/app/pdf/mindmap/display');
  const showNavigationBar = navBarVisibleRoutes.includes(location.pathname) && !isMindMapDisplayPage;
  const isPaymentStatusPage =
    location.pathname === "/payment/success" ||
    location.pathname === "/payment/cancel";

  return (
    <div className="flex flex-col min-h-screen bg-black">
      {showNavigationBar && !isPaymentStatusPage && (
        <NavigationBar
          isMenuOpen={isMenuOpen}
          setIsMenuOpen={setIsMenuOpen}
          onGetStartedClick={handleGetStartedClick}
        />
      )}

      <div className="flex-grow flex flex-col overflow-hidden">
        <AnimatePresence mode="wait">
          <Suspense fallback={<GlobalSpinner />}>
            <Routes location={location} key={location.pathname}>
              <Route path="/" element={<HomePage />} />
              <Route
                path="/main"
                element={
                  <LayoutManager
                    showSidebar={false}
                    openLoginModal={openLoginModal}
                    openAuthPromptModal={openAuthPromptModal}
                    openComingSoonModal={openComingSoonModal}
                  >
                    <AnimatedPageWrapper>
                      <MainPage openComingSoonModal={openComingSoonModal} />
                    </AnimatedPageWrapper>
                  </LayoutManager>
                }
              />
              <Route
                path="/verify-email"
                element={
                  <div className="min-h-screen flex items-center justify-center p-4 bg-slate-900">
                    <Suspense fallback={<GlobalSpinner />}>
                      <VerificationModalContent
                        onSuccess={handleVerificationSuccess}
                        onBackToRegister={handleBackToRegisterFromAuthModals}
                      />
                    </Suspense>
                  </div>
                }
              />

              <Route
                path="/app/pdf/mindmap/display/:id"
                element={
                  <ProtectedRoute>
                    <AnimatedPageWrapper fullScreen>
                      <MindMapDisplayPage />
                    </AnimatedPageWrapper>
                  </ProtectedRoute>
                }
              />

              <Route
                path="/app/pdf/*"
                element={
                  <PdfRoutes
                    openLoginModal={openLoginModal}
                    openAuthPromptModal={openAuthPromptModal}
                    openComingSoonModal={openComingSoonModal}
                  />
                }
              />

              {/* --- ADDED BUSINESS ROUTE CONFIGURATION --- */}
              <Route
                path="/app/business/*"
                element={
                  <BusinessRoutes
                    openLoginModal={openLoginModal}
                    openAuthPromptModal={openAuthPromptModal}
                    openComingSoonModal={openComingSoonModal}
                  />
                }
              />
              {/* --- END OF ADDITION --- */}

              <Route
                path="/app/ai-characters/*"
                element={
                  <AICharactersRoutes
                    openLoginModal={openLoginModal}
                    openAuthPromptModal={openAuthPromptModal}
                    openComingSoonModal={openComingSoonModal}
                  />
                }
              />

              {generalAppRoutesConfig.map(
                ({ path, LazyPageElement, isProtected, showSidebar }) => {
                  let pageContent = (
                    <AnimatedPageWrapper>
                      <LazyPageElement />
                    </AnimatedPageWrapper>
                  );
                  if (isProtected)
                    pageContent = (
                      <ProtectedRoute>{pageContent}</ProtectedRoute>
                    );
                  return (
                    <Route
                      key={path}
                      path={path}
                      element={
                        <LayoutManager
                          showSidebar={showSidebar}
                          openLoginModal={openLoginModal}
                          openAuthPromptModal={openAuthPromptModal}
                          openComingSoonModal={openComingSoonModal}
                        >
                          {pageContent}
                        </LayoutManager>
                      }
                    />
                  );
                }
              )}
              <Route
                path="/pricing"
                element={
                  <AnimatedPageWrapper>
                    <PricingPage />
                  </AnimatedPageWrapper>
                }
              />
              <Route path="/payment/success" element={<PaymentSuccessPage />} />
              <Route path="/payment/cancel" element={<PaymentCancelPage />} />

              {/* Dashboard Route */}
              <Route path="/dashboard/*" element={<Dashboard />} />

              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </Suspense>
        </AnimatePresence>
      </div>

      {/* Modals with Lazy Loaded Content */}
      <Suspense fallback={<div />}>
        {activeModal === "login" && (
          <Modal isOpen={true} onClose={closeModal} title="Sign In">
            <LoginPageContent
              onSwitchToRegister={switchToRegister}
              onSuccess={closeModal}
            />
          </Modal>
        )}
        {activeModal === "register" && (
          <Modal isOpen={true} onClose={closeModal} title="Create Account">
            <RegisterPageContent
              onSwitchToLogin={switchToLogin}
              onSuccess={handleRegisterSuccess}
            />
          </Modal>
        )}
        {activeModal === "verify" &&
          !location.pathname.startsWith("/verify-email") && (
            <Modal isOpen={true} onClose={closeModal} title="Verify Your Email">
              <VerificationModalContent
                onSuccess={handleVerificationSuccess}
                onBackToRegister={handleBackToRegisterFromAuthModals}
              />
            </Modal>
          )}
        {activeModal === "authPrompt" && (
          <Modal
            isOpen={true}
            onClose={closeModal}
            title="Authentication Required"
          >
            <AuthPromptModalContent
              onSwitchToLogin={switchToLogin}
              onSwitchToRegister={switchToRegister}
              onClose={closeModal}
            />
          </Modal>
        )}
        {isComingSoonModalOpen && (
          <ComingSoonModal
            isOpen={true}
            onClose={closeComingSoonModal}
            toolName={currentComingSoonToolName}
          />
        )}
      </Suspense>
    </div>
  );
}

const AppWrapper = () => (
  <BrowserRouter>
    <AuthProvider>
      <ToastProvider>
        <App />
      </ToastProvider>
    </AuthProvider>
  </BrowserRouter>
);

export default AppWrapper;