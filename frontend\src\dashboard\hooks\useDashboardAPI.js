// src/dashboard/hooks/useDashboardAPI.js
import { useState, useCallback } from 'react';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001';

const useDashboardAPI = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const getAuthHeaders = () => {
    const token = localStorage.getItem('adminToken');
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };
  };

  const apiCall = useCallback(async (endpoint, options = {}) => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        headers: getAuthHeaders(),
        ...options
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || `HTTP ${response.status}`);
      }

      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Dashboard Overview
  const getDashboardOverview = useCallback(() => {
    return apiCall('/api/dashboard/overview');
  }, [apiCall]);

  const getSystemAnalytics = useCallback((period = '7d') => {
    return apiCall(`/api/dashboard/analytics?period=${period}`);
  }, [apiCall]);

  const getPerformanceMetrics = useCallback(() => {
    return apiCall('/api/dashboard/performance');
  }, [apiCall]);

  // User Management
  const getUsers = useCallback((params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiCall(`/api/dashboard/users?${queryString}`);
  }, [apiCall]);

  const getUserDetails = useCallback((userId) => {
    return apiCall(`/api/dashboard/users/${userId}`);
  }, [apiCall]);

  const updateUser = useCallback((userId, userData) => {
    return apiCall(`/api/dashboard/users/${userId}`, {
      method: 'PUT',
      body: JSON.stringify(userData)
    });
  }, [apiCall]);

  const deleteUser = useCallback((userId) => {
    return apiCall(`/api/dashboard/users/${userId}`, {
      method: 'DELETE'
    });
  }, [apiCall]);

  const updateUserLimits = useCallback((userId, limits) => {
    return apiCall(`/api/dashboard/users/${userId}/limits`, {
      method: 'PUT',
      body: JSON.stringify({ limits })
    });
  }, [apiCall]);

  const bulkUpdateUsers = useCallback((userIds, updates) => {
    return apiCall('/api/dashboard/users/bulk-update', {
      method: 'PUT',
      body: JSON.stringify({ userIds, updates })
    });
  }, [apiCall]);

  // Admin Management
  const loginAdmin = useCallback(async (email, password) => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`${API_BASE_URL}/api/dashboard/admins/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, password })
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Login failed');
      }

      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const getCurrentAdmin = useCallback(() => {
    return apiCall('/api/dashboard/admins/auth/me');
  }, [apiCall]);

  const changeAdminPassword = useCallback((currentPassword, newPassword) => {
    return apiCall('/api/dashboard/admins/auth/change-password', {
      method: 'PUT',
      body: JSON.stringify({ currentPassword, newPassword })
    });
  }, [apiCall]);

  const createAdmin = useCallback((adminData) => {
    return apiCall('/api/dashboard/admins', {
      method: 'POST',
      body: JSON.stringify(adminData)
    });
  }, [apiCall]);

  const getAllAdmins = useCallback(() => {
    return apiCall('/api/dashboard/admins');
  }, [apiCall]);

  const updateAdmin = useCallback((adminId, adminData) => {
    return apiCall(`/api/dashboard/admins/${adminId}`, {
      method: 'PUT',
      body: JSON.stringify(adminData)
    });
  }, [apiCall]);

  const deleteAdmin = useCallback((adminId) => {
    return apiCall(`/api/dashboard/admins/${adminId}`, {
      method: 'DELETE'
    });
  }, [apiCall]);

  // System Settings
  const getSystemSettings = useCallback(() => {
    return apiCall('/api/dashboard/settings');
  }, [apiCall]);

  const updateSystemSettings = useCallback((settings) => {
    return apiCall('/api/dashboard/settings', {
      method: 'PUT',
      body: JSON.stringify(settings)
    });
  }, [apiCall]);

  const updateDefaultLimits = useCallback((plan, limits) => {
    return apiCall(`/api/dashboard/settings/limits/${plan}`, {
      method: 'PUT',
      body: JSON.stringify(limits)
    });
  }, [apiCall]);

  const updateSystemConfig = useCallback((config) => {
    return apiCall('/api/dashboard/settings/system', {
      method: 'PUT',
      body: JSON.stringify(config)
    });
  }, [apiCall]);

  const updateRateLimiting = useCallback((rateLimits) => {
    return apiCall('/api/dashboard/settings/rate-limiting', {
      method: 'PUT',
      body: JSON.stringify(rateLimits)
    });
  }, [apiCall]);

  const updateSecuritySettings = useCallback((securityConfig) => {
    return apiCall('/api/dashboard/settings/security', {
      method: 'PUT',
      body: JSON.stringify(securityConfig)
    });
  }, [apiCall]);

  const updateFeatureFlags = useCallback((features) => {
    return apiCall('/api/dashboard/settings/features', {
      method: 'PUT',
      body: JSON.stringify(features)
    });
  }, [apiCall]);

  const resetSettingsToDefaults = useCallback(() => {
    return apiCall('/api/dashboard/settings/reset', {
      method: 'POST'
    });
  }, [apiCall]);

  const getSettingsHistory = useCallback((params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiCall(`/api/dashboard/settings/history?${queryString}`);
  }, [apiCall]);

  return {
    loading,
    error,
    // Dashboard Overview
    getDashboardOverview,
    getSystemAnalytics,
    getPerformanceMetrics,
    // User Management
    getUsers,
    getUserDetails,
    updateUser,
    deleteUser,
    updateUserLimits,
    bulkUpdateUsers,
    // Admin Management
    loginAdmin,
    getCurrentAdmin,
    changeAdminPassword,
    createAdmin,
    getAllAdmins,
    updateAdmin,
    deleteAdmin,
    // System Settings
    getSystemSettings,
    updateSystemSettings,
    updateDefaultLimits,
    updateSystemConfig,
    updateRateLimiting,
    updateSecuritySettings,
    updateFeatureFlags,
    resetSettingsToDefaults,
    getSettingsHistory
  };
};

export default useDashboardAPI;
