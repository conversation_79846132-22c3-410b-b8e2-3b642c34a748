// routes/dashboard/index.js
import express from 'express';
import dashboardRoutes from './dashboardRoutes.js';
import userManagementRoutes from './userManagementRoutes.js';
import adminRoutes from './adminRoutes.js';
import systemSettingsRoutes from './systemSettingsRoutes.js';

const router = express.Router();

// Mount all dashboard routes
router.use('/', dashboardRoutes);
router.use('/users', userManagementRoutes);
router.use('/admins', adminRoutes);
router.use('/settings', systemSettingsRoutes);

export default router;
