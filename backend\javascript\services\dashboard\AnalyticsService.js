// services/dashboard/AnalyticsService.js
import User from '../../models/User.js';
import { UserActivity } from '../../models/dashboard/index.js';

/**
 * Service for handling analytics and reporting functionality
 */
class AnalyticsService {
  
  /**
   * Get user growth analytics
   */
  static async getUserGrowthAnalytics(startDate, endDate) {
    try {
      const pipeline = [
        {
          $match: {
            createdAt: { $gte: startDate, $lte: endDate }
          }
        },
        {
          $group: {
            _id: {
              year: { $year: '$createdAt' },
              month: { $month: '$createdAt' },
              day: { $dayOfMonth: '$createdAt' }
            },
            count: { $sum: 1 },
            verified: { $sum: { $cond: ['$isVerified', 1, 0] } },
            plans: {
              $push: '$subscription.planName'
            }
          }
        },
        {
          $project: {
            date: {
              $dateFromParts: {
                year: '$_id.year',
                month: '$_id.month',
                day: '$_id.day'
              }
            },
            totalUsers: '$count',
            verifiedUsers: '$verified',
            unverifiedUsers: { $subtract: ['$count', '$verified'] },
            plans: 1
          }
        },
        { $sort: { date: 1 } }
      ];

      const growthData = await User.aggregate(pipeline);
      
      // Calculate growth rates
      const enrichedData = growthData.map((item, index) => {
        const previousItem = index > 0 ? growthData[index - 1] : null;
        const growthRate = previousItem 
          ? ((item.totalUsers - previousItem.totalUsers) / previousItem.totalUsers * 100).toFixed(2)
          : 0;

        return {
          ...item,
          growthRate: parseFloat(growthRate)
        };
      });

      return enrichedData;
    } catch (error) {
      console.error('Error in getUserGrowthAnalytics:', error);
      throw error;
    }
  }

  /**
   * Get subscription analytics
   */
  static async getSubscriptionAnalytics() {
    try {
      const subscriptionStats = await User.aggregate([
        {
          $group: {
            _id: '$subscription.planName',
            count: { $sum: 1 },
            activeUsers: { 
              $sum: { $cond: [{ $eq: ['$subscription.status', 'active'] }, 1, 0] } 
            },
            totalRevenue: {
              $sum: { $cond: [{ $eq: ['$subscription.planName', 'Pro'] }, 29.99, 0] }
            }
          }
        },
        {
          $project: {
            planName: '$_id',
            userCount: '$count',
            activeUsers: 1,
            totalRevenue: 1,
            conversionRate: {
              $multiply: [
                { $divide: ['$activeUsers', '$count'] },
                100
              ]
            }
          }
        }
      ]);

      // Get monthly subscription trends
      const monthlyTrends = await User.aggregate([
        {
          $match: {
            'subscription.startDate': { $exists: true }
          }
        },
        {
          $group: {
            _id: {
              year: { $year: '$subscription.startDate' },
              month: { $month: '$subscription.startDate' },
              plan: '$subscription.planName'
            },
            count: { $sum: 1 }
          }
        },
        {
          $group: {
            _id: {
              year: '$_id.year',
              month: '$_id.month'
            },
            plans: {
              $push: {
                planName: '$_id.plan',
                count: '$count'
              }
            },
            totalSubscriptions: { $sum: '$count' }
          }
        },
        { $sort: { '_id.year': 1, '_id.month': 1 } }
      ]);

      return {
        overview: subscriptionStats,
        monthlyTrends
      };
    } catch (error) {
      console.error('Error in getSubscriptionAnalytics:', error);
      throw error;
    }
  }

  /**
   * Get feature usage analytics
   */
  static async getFeatureUsageAnalytics(startDate, endDate) {
    try {
      const featureUsage = await UserActivity.aggregate([
        {
          $match: {
            createdAt: { $gte: startDate, $lte: endDate },
            activityType: {
              $in: [
                'ai_chat_message',
                'business_plan_generation',
                'investor_pitch_generation',
                'business_qa_query',
                'pdf_analysis',
                'file_upload'
              ]
            }
          }
        },
        {
          $group: {
            _id: '$activityType',
            totalUsage: { $sum: 1 },
            uniqueUsers: { $addToSet: '$userId' },
            avgProcessingTime: { $avg: '$metadata.processingTime' },
            successRate: {
              $avg: { $cond: ['$success', 1, 0] }
            }
          }
        },
        {
          $project: {
            featureType: '$_id',
            totalUsage: 1,
            uniqueUsers: { $size: '$uniqueUsers' },
            avgProcessingTime: { $round: ['$avgProcessingTime', 2] },
            successRate: { $multiply: ['$successRate', 100] }
          }
        },
        { $sort: { totalUsage: -1 } }
      ]);

      // Get daily usage trends
      const dailyTrends = await UserActivity.aggregate([
        {
          $match: {
            createdAt: { $gte: startDate, $lte: endDate },
            activityType: {
              $in: [
                'ai_chat_message',
                'business_plan_generation',
                'investor_pitch_generation',
                'business_qa_query',
                'pdf_analysis'
              ]
            }
          }
        },
        {
          $group: {
            _id: {
              date: { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } },
              feature: '$activityType'
            },
            count: { $sum: 1 }
          }
        },
        {
          $group: {
            _id: '$_id.date',
            features: {
              $push: {
                type: '$_id.feature',
                count: '$count'
              }
            },
            totalUsage: { $sum: '$count' }
          }
        },
        { $sort: { _id: 1 } }
      ]);

      return {
        overview: featureUsage,
        dailyTrends
      };
    } catch (error) {
      console.error('Error in getFeatureUsageAnalytics:', error);
      throw error;
    }
  }

  /**
   * Get user engagement metrics
   */
  static async getUserEngagementMetrics(startDate, endDate) {
    try {
      const engagementData = await UserActivity.aggregate([
        {
          $match: {
            createdAt: { $gte: startDate, $lte: endDate }
          }
        },
        {
          $group: {
            _id: '$userId',
            totalActivities: { $sum: 1 },
            uniqueFeatures: { $addToSet: '$activityType' },
            firstActivity: { $min: '$createdAt' },
            lastActivity: { $max: '$createdAt' },
            avgSessionDuration: { $avg: '$context.sessionDuration' }
          }
        },
        {
          $project: {
            userId: '$_id',
            totalActivities: 1,
            uniqueFeaturesCount: { $size: '$uniqueFeatures' },
            daysSinceFirstActivity: {
              $divide: [
                { $subtract: [endDate, '$firstActivity'] },
                1000 * 60 * 60 * 24
              ]
            },
            daysSinceLastActivity: {
              $divide: [
                { $subtract: [endDate, '$lastActivity'] },
                1000 * 60 * 60 * 24
              ]
            },
            avgSessionDuration: { $round: ['$avgSessionDuration', 2] },
            engagementScore: {
              $multiply: [
                { $log: [{ $add: ['$totalActivities', 1] }, 10] },
                '$uniqueFeaturesCount'
              ]
            }
          }
        }
      ]);

      // Calculate engagement segments
      const segments = {
        highlyEngaged: engagementData.filter(user => user.engagementScore > 10).length,
        moderatelyEngaged: engagementData.filter(user => user.engagementScore > 5 && user.engagementScore <= 10).length,
        lowEngaged: engagementData.filter(user => user.engagementScore <= 5).length
      };

      // Calculate retention metrics
      const retentionData = await this.calculateRetentionMetrics(startDate, endDate);

      return {
        userEngagement: engagementData.slice(0, 100), // Top 100 most engaged users
        segments,
        retention: retentionData,
        summary: {
          totalActiveUsers: engagementData.length,
          avgEngagementScore: engagementData.reduce((sum, user) => sum + user.engagementScore, 0) / engagementData.length,
          avgSessionDuration: engagementData.reduce((sum, user) => sum + (user.avgSessionDuration || 0), 0) / engagementData.length
        }
      };
    } catch (error) {
      console.error('Error in getUserEngagementMetrics:', error);
      throw error;
    }
  }

  /**
   * Calculate user retention metrics
   */
  static async calculateRetentionMetrics(startDate, endDate) {
    try {
      // Get users who registered in the period
      const newUsers = await User.find({
        createdAt: { $gte: startDate, $lte: endDate }
      }).select('_id createdAt');

      const retentionData = [];

      for (const user of newUsers) {
        const daysSinceRegistration = Math.floor((endDate - user.createdAt) / (1000 * 60 * 60 * 24));
        
        // Check activity in different periods
        const day1Activity = await UserActivity.countDocuments({
          userId: user._id,
          createdAt: {
            $gte: new Date(user.createdAt.getTime() + 24 * 60 * 60 * 1000),
            $lte: new Date(user.createdAt.getTime() + 2 * 24 * 60 * 60 * 1000)
          }
        });

        const day7Activity = await UserActivity.countDocuments({
          userId: user._id,
          createdAt: {
            $gte: new Date(user.createdAt.getTime() + 7 * 24 * 60 * 60 * 1000),
            $lte: new Date(user.createdAt.getTime() + 8 * 24 * 60 * 60 * 1000)
          }
        });

        const day30Activity = await UserActivity.countDocuments({
          userId: user._id,
          createdAt: {
            $gte: new Date(user.createdAt.getTime() + 30 * 24 * 60 * 60 * 1000),
            $lte: new Date(user.createdAt.getTime() + 31 * 24 * 60 * 60 * 1000)
          }
        });

        retentionData.push({
          userId: user._id,
          registrationDate: user.createdAt,
          daysSinceRegistration,
          day1Retained: day1Activity > 0,
          day7Retained: day7Activity > 0,
          day30Retained: day30Activity > 0
        });
      }

      // Calculate retention rates
      const totalUsers = retentionData.length;
      const day1Retention = retentionData.filter(u => u.day1Retained).length / totalUsers * 100;
      const day7Retention = retentionData.filter(u => u.day7Retained).length / totalUsers * 100;
      const day30Retention = retentionData.filter(u => u.day30Retained).length / totalUsers * 100;

      return {
        totalNewUsers: totalUsers,
        day1Retention: parseFloat(day1Retention.toFixed(2)),
        day7Retention: parseFloat(day7Retention.toFixed(2)),
        day30Retention: parseFloat(day30Retention.toFixed(2)),
        detailedData: retentionData
      };
    } catch (error) {
      console.error('Error in calculateRetentionMetrics:', error);
      throw error;
    }
  }

  /**
   * Get revenue analytics
   */
  static async getRevenueAnalytics(startDate, endDate) {
    try {
      const revenueData = await UserActivity.aggregate([
        {
          $match: {
            createdAt: { $gte: startDate, $lte: endDate },
            activityType: 'payment_completed',
            'metadata.amount': { $exists: true }
          }
        },
        {
          $group: {
            _id: {
              date: { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } },
              currency: '$metadata.currency'
            },
            totalRevenue: { $sum: '$metadata.amount' },
            transactionCount: { $sum: 1 },
            uniqueCustomers: { $addToSet: '$userId' }
          }
        },
        {
          $project: {
            date: '$_id.date',
            currency: '$_id.currency',
            totalRevenue: 1,
            transactionCount: 1,
            uniqueCustomers: { $size: '$uniqueCustomers' },
            avgTransactionValue: { $divide: ['$totalRevenue', '$transactionCount'] }
          }
        },
        { $sort: { date: 1 } }
      ]);

      return revenueData;
    } catch (error) {
      console.error('Error in getRevenueAnalytics:', error);
      throw error;
    }
  }
}

export default AnalyticsService;
