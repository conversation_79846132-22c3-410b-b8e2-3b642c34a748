// routes/dashboard/dashboardRoutes.js
import express from 'express';
import {
  getDashboardOverview,
  getSystemAnalytics,
  getPerformanceMetrics
} from '../../controllers/dashboard/index.js';
import { protectAdmin, requirePermission } from '../../middleware/adminAuthMiddleware.js';

const router = express.Router();

// Dashboard overview and analytics routes
router.get('/overview', protectAdmin, requirePermission('analytics'), getDashboardOverview);
router.get('/analytics', protectAdmin, requirePermission('analytics'), getSystemAnalytics);
router.get('/performance', protectAdmin, requirePermission('analytics'), getPerformanceMetrics);

export default router;
