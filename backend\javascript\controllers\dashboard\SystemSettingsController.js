// controllers/dashboard/SystemSettingsController.js
import { SystemSettings, UserActivity } from '../../models/dashboard/index.js';

/**
 * @desc    Get current system settings
 * @route   GET /api/dashboard/settings
 * @access  Private (Admin only)
 */
export const getSystemSettings = async (req, res) => {
  try {
    const settings = await SystemSettings.getCurrentSettings();
    
    res.json({
      success: true,
      data: { settings }
    });

  } catch (error) {
    console.error('Error in getSystemSettings:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Server error while fetching system settings.' 
    });
  }
};

/**
 * @desc    Update system settings
 * @route   PUT /api/dashboard/settings
 * @access  Private (Super Admin only)
 */
export const updateSystemSettings = async (req, res) => {
  try {
    const updates = req.body;
    
    const settings = await SystemSettings.getCurrentSettings();
    
    // Update only provided fields
    Object.keys(updates).forEach(key => {
      if (settings[key] !== undefined) {
        if (typeof settings[key] === 'object' && !Array.isArray(settings[key])) {
          // For nested objects, merge the updates
          Object.assign(settings[key], updates[key]);
        } else {
          settings[key] = updates[key];
        }
      }
    });

    settings.lastUpdatedBy = req.admin.id;
    await settings.save();

    // Log the settings update
    await UserActivity.create({
      userId: null,
      activityType: 'system_settings_update',
      metadata: {
        updatedBy: req.admin.id,
        updatedFields: Object.keys(updates)
      },
      success: true
    });

    res.json({
      success: true,
      message: 'System settings updated successfully.',
      data: { settings }
    });

  } catch (error) {
    console.error('Error in updateSystemSettings:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Server error while updating system settings.' 
    });
  }
};

/**
 * @desc    Update default limits for a specific plan
 * @route   PUT /api/dashboard/settings/limits/:plan
 * @access  Private (Super Admin only)
 */
export const updateDefaultLimits = async (req, res) => {
  try {
    const { plan } = req.params;
    const limits = req.body;

    if (!['starter', 'pro', 'enterprise'].includes(plan)) {
      return res.status(400).json({ 
        success: false, 
        error: 'Invalid plan. Must be starter, pro, or enterprise.' 
      });
    }

    const settings = await SystemSettings.getCurrentSettings();
    
    // Update the specific plan limits
    Object.assign(settings.defaultLimits[plan], limits);
    settings.lastUpdatedBy = req.admin.id;
    
    await settings.save();

    // Log the limits update
    await UserActivity.create({
      userId: null,
      activityType: 'default_limits_update',
      metadata: {
        updatedBy: req.admin.id,
        plan: plan,
        newLimits: limits
      },
      success: true
    });

    res.json({
      success: true,
      message: `Default limits for ${plan} plan updated successfully.`,
      data: { 
        plan,
        limits: settings.defaultLimits[plan]
      }
    });

  } catch (error) {
    console.error('Error in updateDefaultLimits:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Server error while updating default limits.' 
    });
  }
};

/**
 * @desc    Update system configuration
 * @route   PUT /api/dashboard/settings/system
 * @access  Private (Super Admin only)
 */
export const updateSystemConfig = async (req, res) => {
  try {
    const config = req.body;
    
    const settings = await SystemSettings.getCurrentSettings();
    
    // Update system configuration
    Object.assign(settings.systemConfig, config);
    settings.lastUpdatedBy = req.admin.id;
    
    await settings.save();

    // Log the system config update
    await UserActivity.create({
      userId: null,
      activityType: 'system_config_update',
      metadata: {
        updatedBy: req.admin.id,
        updatedConfig: Object.keys(config)
      },
      success: true
    });

    res.json({
      success: true,
      message: 'System configuration updated successfully.',
      data: { 
        systemConfig: settings.systemConfig
      }
    });

  } catch (error) {
    console.error('Error in updateSystemConfig:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Server error while updating system configuration.' 
    });
  }
};

/**
 * @desc    Update rate limiting settings
 * @route   PUT /api/dashboard/settings/rate-limiting
 * @access  Private (Super Admin only)
 */
export const updateRateLimiting = async (req, res) => {
  try {
    const rateLimits = req.body;
    
    const settings = await SystemSettings.getCurrentSettings();
    
    // Update rate limiting settings
    Object.assign(settings.rateLimiting, rateLimits);
    settings.lastUpdatedBy = req.admin.id;
    
    await settings.save();

    // Log the rate limiting update
    await UserActivity.create({
      userId: null,
      activityType: 'rate_limiting_update',
      metadata: {
        updatedBy: req.admin.id,
        newRateLimits: rateLimits
      },
      success: true
    });

    res.json({
      success: true,
      message: 'Rate limiting settings updated successfully.',
      data: { 
        rateLimiting: settings.rateLimiting
      }
    });

  } catch (error) {
    console.error('Error in updateRateLimiting:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Server error while updating rate limiting settings.' 
    });
  }
};

/**
 * @desc    Update security settings
 * @route   PUT /api/dashboard/settings/security
 * @access  Private (Super Admin only)
 */
export const updateSecuritySettings = async (req, res) => {
  try {
    const securityConfig = req.body;
    
    const settings = await SystemSettings.getCurrentSettings();
    
    // Update security settings
    Object.assign(settings.security, securityConfig);
    settings.lastUpdatedBy = req.admin.id;
    
    await settings.save();

    // Log the security settings update
    await UserActivity.create({
      userId: null,
      activityType: 'security_settings_update',
      metadata: {
        updatedBy: req.admin.id,
        updatedSecurityConfig: Object.keys(securityConfig)
      },
      success: true
    });

    res.json({
      success: true,
      message: 'Security settings updated successfully.',
      data: { 
        security: settings.security
      }
    });

  } catch (error) {
    console.error('Error in updateSecuritySettings:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Server error while updating security settings.' 
    });
  }
};

/**
 * @desc    Update feature flags
 * @route   PUT /api/dashboard/settings/features
 * @access  Private (Super Admin only)
 */
export const updateFeatureFlags = async (req, res) => {
  try {
    const features = req.body;
    
    const settings = await SystemSettings.getCurrentSettings();
    
    // Update feature flags
    Object.assign(settings.features, features);
    settings.lastUpdatedBy = req.admin.id;
    
    await settings.save();

    // Log the feature flags update
    await UserActivity.create({
      userId: null,
      activityType: 'feature_flags_update',
      metadata: {
        updatedBy: req.admin.id,
        updatedFeatures: Object.keys(features)
      },
      success: true
    });

    res.json({
      success: true,
      message: 'Feature flags updated successfully.',
      data: { 
        features: settings.features
      }
    });

  } catch (error) {
    console.error('Error in updateFeatureFlags:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Server error while updating feature flags.' 
    });
  }
};

/**
 * @desc    Reset settings to defaults
 * @route   POST /api/dashboard/settings/reset
 * @access  Private (Super Admin only)
 */
export const resetToDefaults = async (req, res) => {
  try {
    const settings = await SystemSettings.getCurrentSettings();
    
    await settings.resetToDefaults();
    settings.lastUpdatedBy = req.admin.id;
    await settings.save();

    // Log the settings reset
    await UserActivity.create({
      userId: null,
      activityType: 'system_settings_reset',
      metadata: {
        resetBy: req.admin.id
      },
      success: true
    });

    res.json({
      success: true,
      message: 'System settings reset to defaults successfully.',
      data: { settings }
    });

  } catch (error) {
    console.error('Error in resetToDefaults:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Server error while resetting settings.' 
    });
  }
};

/**
 * @desc    Get settings history/audit log
 * @route   GET /api/dashboard/settings/history
 * @access  Private (Admin only)
 */
export const getSettingsHistory = async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    const settingsActivities = await UserActivity.find({
      activityType: { 
        $in: [
          'system_settings_update',
          'default_limits_update',
          'system_config_update',
          'rate_limiting_update',
          'security_settings_update',
          'feature_flags_update',
          'system_settings_reset'
        ]
      }
    })
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limitNum)
    .lean();

    const totalCount = await UserActivity.countDocuments({
      activityType: { 
        $in: [
          'system_settings_update',
          'default_limits_update',
          'system_config_update',
          'rate_limiting_update',
          'security_settings_update',
          'feature_flags_update',
          'system_settings_reset'
        ]
      }
    });

    const totalPages = Math.ceil(totalCount / limitNum);

    res.json({
      success: true,
      data: {
        history: settingsActivities,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalCount,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1
        }
      }
    });

  } catch (error) {
    console.error('Error in getSettingsHistory:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Server error while fetching settings history.' 
    });
  }
};
