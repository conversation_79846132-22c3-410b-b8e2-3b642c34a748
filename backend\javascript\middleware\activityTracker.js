// middleware/activityTracker.js
import { UserActivity } from '../models/dashboard/index.js';

/**
 * Middleware to track user activity for dashboard analytics
 */
const trackActivity = (activityType, options = {}) => {
  return async (req, res, next) => {
    // Store original res.json to capture response
    const originalJson = res.json;
    
    res.json = function(data) {
      // Call original json method
      const result = originalJson.call(this, data);
      
      // Track activity asynchronously (don't block response)
      setImmediate(async () => {
        try {
          const userId = req.user?.id || null;
          const success = res.statusCode >= 200 && res.statusCode < 400;
          
          // Extract metadata based on activity type and request
          const metadata = extractMetadata(req, res, activityType, options);
          
          // Extract request info
          const requestInfo = {
            method: req.method,
            endpoint: req.originalUrl,
            statusCode: res.statusCode,
            userAgent: req.get('User-Agent'),
            ipAddress: req.ip || req.connection.remoteAddress,
          };
          
          // Extract device info from user agent
          const deviceInfo = parseUserAgent(req.get('User-Agent'));
          
          // Create activity record
          await UserActivity.create({
            userId,
            activityType,
            metadata,
            requestInfo,
            deviceInfo,
            success,
            context: {
              sessionId: req.sessionID,
              referrer: req.get('Referer'),
              subscriptionPlan: req.user?.subscription?.planName,
            }
          });
          
        } catch (error) {
          console.error('Error tracking activity:', error);
          // Don't throw error to avoid affecting the main request
        }
      });
      
      return result;
    };
    
    next();
  };
};

/**
 * Extract metadata based on activity type
 */
const extractMetadata = (req, res, activityType, options) => {
  const metadata = {};
  
  switch (activityType) {
    case 'file_upload':
      if (req.file) {
        metadata.fileName = req.file.originalname;
        metadata.fileSize = req.file.size;
        metadata.fileType = req.file.mimetype;
      }
      break;
      
    case 'ai_chat_message':
      if (req.body.message) {
        metadata.promptLength = req.body.message.length;
      }
      break;
      
    case 'business_plan_generation':
    case 'investor_pitch_generation':
    case 'business_qa_query':
      if (req.body.prompt) {
        metadata.promptLength = req.body.prompt.length;
      }
      break;
      
    case 'payment_initiated':
    case 'payment_completed':
    case 'payment_failed':
      if (req.body.amount) {
        metadata.amount = req.body.amount;
        metadata.currency = req.body.currency || 'USD';
      }
      if (req.body.transactionId) {
        metadata.transactionId = req.body.transactionId;
      }
      break;
      
    case 'error_occurred':
      if (res.statusCode >= 400) {
        metadata.errorCode = res.statusCode;
        metadata.errorMessage = res.statusMessage;
      }
      break;
      
    case 'feature_access':
      if (options.featureName) {
        metadata.featureName = options.featureName;
      }
      break;
  }
  
  // Add custom metadata from options
  if (options.metadata) {
    Object.assign(metadata, options.metadata);
  }
  
  return metadata;
};

/**
 * Parse user agent to extract device information
 */
const parseUserAgent = (userAgent) => {
  if (!userAgent) return {};
  
  const deviceInfo = {
    platform: 'web',
    browser: 'Unknown',
    browserVersion: 'Unknown',
    os: 'Unknown',
    osVersion: 'Unknown',
    deviceType: 'desktop'
  };
  
  // Detect browser
  if (userAgent.includes('Chrome')) {
    deviceInfo.browser = 'Chrome';
    const match = userAgent.match(/Chrome\/([0-9.]+)/);
    if (match) deviceInfo.browserVersion = match[1];
  } else if (userAgent.includes('Firefox')) {
    deviceInfo.browser = 'Firefox';
    const match = userAgent.match(/Firefox\/([0-9.]+)/);
    if (match) deviceInfo.browserVersion = match[1];
  } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
    deviceInfo.browser = 'Safari';
    const match = userAgent.match(/Version\/([0-9.]+)/);
    if (match) deviceInfo.browserVersion = match[1];
  } else if (userAgent.includes('Edge')) {
    deviceInfo.browser = 'Edge';
    const match = userAgent.match(/Edge\/([0-9.]+)/);
    if (match) deviceInfo.browserVersion = match[1];
  }
  
  // Detect OS
  if (userAgent.includes('Windows')) {
    deviceInfo.os = 'Windows';
    if (userAgent.includes('Windows NT 10.0')) deviceInfo.osVersion = '10';
    else if (userAgent.includes('Windows NT 6.3')) deviceInfo.osVersion = '8.1';
    else if (userAgent.includes('Windows NT 6.2')) deviceInfo.osVersion = '8';
    else if (userAgent.includes('Windows NT 6.1')) deviceInfo.osVersion = '7';
  } else if (userAgent.includes('Mac OS X')) {
    deviceInfo.os = 'macOS';
    const match = userAgent.match(/Mac OS X ([0-9_]+)/);
    if (match) deviceInfo.osVersion = match[1].replace(/_/g, '.');
  } else if (userAgent.includes('Linux')) {
    deviceInfo.os = 'Linux';
  } else if (userAgent.includes('Android')) {
    deviceInfo.os = 'Android';
    deviceInfo.platform = 'mobile';
    deviceInfo.deviceType = 'mobile';
    const match = userAgent.match(/Android ([0-9.]+)/);
    if (match) deviceInfo.osVersion = match[1];
  } else if (userAgent.includes('iOS') || userAgent.includes('iPhone') || userAgent.includes('iPad')) {
    deviceInfo.os = 'iOS';
    deviceInfo.platform = 'mobile';
    deviceInfo.deviceType = userAgent.includes('iPad') ? 'tablet' : 'mobile';
    const match = userAgent.match(/OS ([0-9_]+)/);
    if (match) deviceInfo.osVersion = match[1].replace(/_/g, '.');
  }
  
  // Detect device type
  if (userAgent.includes('Mobile') && !userAgent.includes('iPad')) {
    deviceInfo.deviceType = 'mobile';
  } else if (userAgent.includes('Tablet') || userAgent.includes('iPad')) {
    deviceInfo.deviceType = 'tablet';
  }
  
  return deviceInfo;
};

/**
 * Specific activity trackers for common actions
 */
export const trackLogin = trackActivity('login');
export const trackLogout = trackActivity('logout');
export const trackRegistration = trackActivity('registration');
export const trackFileUpload = trackActivity('file_upload');
export const trackAIChatMessage = trackActivity('ai_chat_message');
export const trackBusinessPlanGeneration = trackActivity('business_plan_generation');
export const trackInvestorPitchGeneration = trackActivity('investor_pitch_generation');
export const trackBusinessQAQuery = trackActivity('business_qa_query');
export const trackPDFAnalysis = trackActivity('pdf_analysis');
export const trackPaymentInitiated = trackActivity('payment_initiated');
export const trackPaymentCompleted = trackActivity('payment_completed');
export const trackPaymentFailed = trackActivity('payment_failed');

/**
 * Track feature access
 */
export const trackFeatureAccess = (featureName, planRequired = null) => {
  return trackActivity('feature_access', {
    metadata: { featureName, planRequired }
  });
};

/**
 * Track limit exceeded
 */
export const trackLimitExceeded = (limitType, currentUsage, limit) => {
  return trackActivity('limit_exceeded', {
    metadata: { limitType, currentUsage, limit }
  });
};

export default trackActivity;
