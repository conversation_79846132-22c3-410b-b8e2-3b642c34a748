# Admin Dashboard System

A comprehensive admin dashboard system for managing users, analytics, and system settings with role-based access control.

## Features

### 🔐 Authentication & Authorization
- Secure admin login with JWT tokens
- Role-based access control (Super Admin, Admin, Moderator)
- Permission-based route protection
- Account lockout after failed login attempts

### 👥 User Management
- View all registered users with pagination and filtering
- User search by name and email
- Edit user details and subscription plans
- Manage user limits and usage counts
- Bulk user operations
- User activity tracking

### 📊 Analytics & Monitoring
- Dashboard overview with key metrics
- User growth analytics
- Feature usage statistics
- System performance monitoring
- Real-time activity tracking
- Error analysis and reporting

### ⚙️ System Settings
- Configure default limits for different plans
- System-wide configuration options
- Rate limiting settings
- Security settings management
- Feature flags control
- Settings history and audit log

### 🛡️ Security Features
- Admin authentication with enhanced security
- Role-based permissions system
- Activity logging and monitoring
- Account lockout protection
- Secure password requirements

## Architecture

### Backend Structure
```
backend/javascript/
├── controllers/dashboard/
│   ├── DashboardController.js      # Overview and analytics
│   ├── UserManagementController.js # User CRUD operations
│   ├── AdminController.js          # Admin management
│   └── SystemSettingsController.js # Settings management
├── models/dashboard/
│   ├── Admin.js                    # Admin user model
│   ├── SystemSettings.js           # System configuration
│   └── UserActivity.js             # Activity tracking
├── routes/dashboard/
│   ├── dashboardRoutes.js          # Dashboard routes
│   ├── userManagementRoutes.js     # User management routes
│   ├── adminRoutes.js              # Admin routes
│   └── systemSettingsRoutes.js     # Settings routes
├── services/dashboard/
│   ├── AnalyticsService.js         # Analytics business logic
│   └── UserManagementService.js    # User management logic
└── middleware/
    ├── adminAuthMiddleware.js      # Admin authentication
    └── activityTracker.js          # Activity tracking
```

### Frontend Structure
```
frontend/src/dashboard/
├── components/
│   ├── DataTable.jsx               # Reusable data table
│   ├── StatsCard.jsx               # Statistics cards
│   ├── DashboardLayout.jsx         # Main layout
│   ├── Modal.jsx                   # Modal component
│   ├── LoadingSpinner.jsx          # Loading indicators
│   ├── Chart.jsx                   # Chart component
│   ├── ProtectedRoute.jsx          # Route protection
│   ├── ErrorBoundary.jsx           # Error handling
│   └── Toast.jsx                   # Notifications
├── pages/
│   ├── DashboardOverview.jsx       # Main dashboard
│   ├── UserManagement.jsx          # User management
│   └── AdminLogin.jsx              # Admin login
├── hooks/
│   ├── useDashboardAPI.js          # API integration
│   ├── useAdminAuth.js             # Authentication
│   ├── usePagination.js            # Pagination logic
│   └── useFilters.js               # Filtering logic
└── utils/
    ├── formatters.js               # Data formatting
    ├── validators.js               # Input validation
    ├── helpers.js                  # Utility functions
    └── constants.js                # Constants
```

## Setup Instructions

### 1. Backend Setup

1. **Install Dependencies**
   ```bash
   cd backend/javascript
   npm install
   ```

2. **Create Super Admin**
   ```bash
   node scripts/createSuperAdmin.js <EMAIL> SuperAdmin123! "Super Administrator"
   ```

3. **Environment Variables**
   Ensure these variables are set in your `.env` file:
   ```
   JWT_SECRET=your_jwt_secret_here
   JWT_EXPIRY_DURATION=24h
   MONGODB_URI=your_mongodb_connection_string
   ```

### 2. Frontend Setup

1. **Install Dependencies**
   ```bash
   cd frontend
   npm install
   ```

2. **Update API URL**
   Update the API base URL in `src/dashboard/hooks/useDashboardAPI.js` if needed.

### 3. Access the Dashboard

1. Start the backend server
2. Start the frontend development server
3. Navigate to `http://localhost:3000/dashboard`
4. Login with the super admin credentials created in step 2

## API Endpoints

### Authentication
- `POST /api/dashboard/admins/auth/login` - Admin login
- `GET /api/dashboard/admins/auth/me` - Get current admin
- `PUT /api/dashboard/admins/auth/change-password` - Change password

### Dashboard
- `GET /api/dashboard/overview` - Dashboard overview
- `GET /api/dashboard/analytics` - System analytics
- `GET /api/dashboard/performance` - Performance metrics

### User Management
- `GET /api/dashboard/users` - Get all users
- `GET /api/dashboard/users/:id` - Get user details
- `PUT /api/dashboard/users/:id` - Update user
- `DELETE /api/dashboard/users/:id` - Delete user
- `PUT /api/dashboard/users/:id/limits` - Update user limits
- `PUT /api/dashboard/users/bulk-update` - Bulk update users

### Admin Management
- `POST /api/dashboard/admins` - Create admin
- `GET /api/dashboard/admins` - Get all admins
- `PUT /api/dashboard/admins/:id` - Update admin
- `DELETE /api/dashboard/admins/:id` - Delete admin

### System Settings
- `GET /api/dashboard/settings` - Get system settings
- `PUT /api/dashboard/settings` - Update system settings
- `PUT /api/dashboard/settings/limits/:plan` - Update plan limits
- `PUT /api/dashboard/settings/system` - Update system config
- `PUT /api/dashboard/settings/security` - Update security settings
- `POST /api/dashboard/settings/reset` - Reset to defaults

## Permissions

### Super Admin
- Full access to all features
- Can manage other admins
- Can modify system settings
- Can access all analytics

### Admin
- User management
- Limits management
- Analytics access
- Cannot manage other admins
- Cannot modify system settings

### Moderator
- User management (limited)
- Analytics access (limited)
- Cannot manage limits
- Cannot manage admins
- Cannot modify settings

## Security Considerations

1. **Authentication**
   - JWT tokens with expiration
   - Secure password requirements
   - Account lockout after failed attempts

2. **Authorization**
   - Role-based access control
   - Permission-based route protection
   - API endpoint protection

3. **Data Protection**
   - Input validation and sanitization
   - SQL injection prevention
   - XSS protection

4. **Monitoring**
   - Activity logging
   - Error tracking
   - Performance monitoring

## Responsive Design

The dashboard is fully responsive and works on:
- Desktop computers (1024px+)
- Tablets (768px - 1023px)
- Mobile devices (320px - 767px)

## Browser Support

- Chrome 70+
- Firefox 65+
- Safari 12+
- Edge 79+

## Contributing

1. Follow the existing code structure
2. Add proper error handling
3. Include input validation
4. Write comprehensive tests
5. Update documentation

## License

This dashboard system is part of the Dosky platform and follows the same licensing terms.
